using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;

namespace GraphQLApi.Data.Configurations
{
    public class GeneralWorkPermitConfiguration : IEntityTypeConfiguration<GeneralWorkPermit>
    {
        public void Configure(EntityTypeBuilder<GeneralWorkPermit> builder)
        {
            builder.Property(p => p.Isolation)
                .HasMaxLength(1000);

            // WorkAreaInspectionAndPermitRenewal is now configured as owned entity in AppDbContext
            builder.OwnsMany(
    p => p.WorkAreaInspectionAndPermitRenewal, OwnedNavigationBuilder =>
    {
        OwnedNavigationBuilder.ToJson();
    }
);

        }
    }
}
