using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;

namespace GraphQLApi.Data.Configurations
{
    public class ConfinedSpacePermitConfiguration : IEntityTypeConfiguration<ConfinedSpacePermit>
    {
        public void Configure(EntityTypeBuilder<ConfinedSpacePermit> builder)
        {
            builder.Property(p => p.WorkersHaveBeenTrained)
                .IsRequired();

            builder.Property(p => p.NameOfTrainingOrganization)
                .HasMaxLength(200);

            builder.Property(p => p.EmergencyGuidelines)
                .HasMaxLength(2000);

            // TopReading, MidReading, BottomReading, and TaskObserver are now configured as owned entities in AppDbContext
            
            builder.OwnsOne(
                p => p.TopReading, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson();
                }
            );

            builder.OwnsOne(
                p => p.MidReading, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson();
                }
            );

            builder.OwnsOne(
                p => p.BottomReading, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson();
                }
            );

            builder.OwnsOne(
                p => p.TaskObserver, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson();
                }
            );
        }
    }
}
