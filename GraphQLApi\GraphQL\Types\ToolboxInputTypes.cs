using HotChocolate.Types;

namespace GraphQLApi.GraphQL.Types
{
    // GraphQL Input Types for Toolbox operations
    public class CreateToolboxInput
    {
        public int ConductorId { get; set; }
        public List<CreateToolboxJobInput> Jobs { get; set; } = new();
        public string EmergencyProcedures { get; set; } = string.Empty;
        public string ToolboxTrainingTopics { get; set; } = string.Empty;
    }

    public class CreateToolboxJobInput
    {
        public int JobId { get; set; }
        public CreateToolboxHazardInput Hazard { get; set; } = null!;
    }

    public class CreateToolboxHazardInput
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<CreateToolboxControlMeasureInput> ControlMeasures { get; set; } = new();
    }

    public class CreateToolboxControlMeasureInput
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class AddAttendeesInput
    {
        public int ToolboxId { get; set; }
        public List<ToolboxAttendeeInput> Workers { get; set; } = new();
    }

    public class ToolboxAttendeeInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
    }

    public class SummarizeToolboxInput
    {
        public int ToolboxId { get; set; }
        public List<SummarizeToolboxJobInput> Jobs { get; set; } = new();
    }

    public class SummarizeToolboxJobInput
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public SummarizeToolboxHazardInput Hazards { get; set; } = null!;
    }

    public class SummarizeToolboxHazardInput
    {
        public string Description { get; set; } = string.Empty;
        public List<SummarizeToolboxControlMeasureInput> ControlMeasures { get; set; } = new();
    }

    public class SummarizeToolboxControlMeasureInput
    {
        public string Description { get; set; } = string.Empty;
    }

    public class AddHazardInput
    {
        public int JobId { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<string> ControlMeasures { get; set; } = new();
    }

    public class AddControlMeasureInput
    {
        public int HazardId { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    // Output types for GraphQL
    public class TodaysJobRiskAssessment
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public TodaysJobHazard Hazards { get; set; } = null!;
    }

    public class TodaysJobHazard
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<TodaysJobControlMeasure> ControlMeasures { get; set; } = new();
    }

    public class TodaysJobControlMeasure
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    // GraphQL Type definitions for the output types
    public class TodaysJobRiskAssessmentType : ObjectType<TodaysJobRiskAssessment>
    {
        protected override void Configure(IObjectTypeDescriptor<TodaysJobRiskAssessment> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Title).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Hazards).Type<TodaysJobHazardType>();
        }
    }

    public class TodaysJobHazardType : ObjectType<TodaysJobHazard>
    {
        protected override void Configure(IObjectTypeDescriptor<TodaysJobHazard> descriptor)
        {
            descriptor.Field(h => h.Id).Type<NonNullType<IntType>>();
            descriptor.Field(h => h.Description).Type<NonNullType<StringType>>();
            descriptor.Field(h => h.ControlMeasures).Type<ListType<TodaysJobControlMeasureType>>();
        }
    }

    public class TodaysJobControlMeasureType : ObjectType<TodaysJobControlMeasure>
    {
        protected override void Configure(IObjectTypeDescriptor<TodaysJobControlMeasure> descriptor)
        {
            descriptor.Field(cm => cm.Id).Type<NonNullType<IntType>>();
            descriptor.Field(cm => cm.Description).Type<NonNullType<StringType>>();
        }
    }
}
