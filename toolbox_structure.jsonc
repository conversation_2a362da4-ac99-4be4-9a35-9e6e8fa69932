{
    // add a closed type bool column in control measure table, default is false
    "date": "datetime",
    "conductor": {
        "workerId": "int",
        "name": "string",
        "signatureFileId": "string"
    },
    "jobs": "[job]", // one to many relationship with job table
    "emergency procedures": "string",
    "toolbox training topics": "string",
    "attendees": [ // json array of shape below
        {
            "workerId": "int",
            "name": "string",
            "designation": "string", // training for the worker
            "signatureFileId": "string"
        }
    ],
    "conducted by": { // json object of shape below
        "workerId": "int",
        "name": "string",
        "signatureFileId": "string"
    },
    "closed date": "datetime"
}