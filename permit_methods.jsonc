{
    // comment 1: make a one to many relationship between job and permit
    // comment 2: permits have only one day valid, only the day of creation not 24hrs except general permits that have 7 days (counted as day of creation as day 1, next day day 2, etc.. not as 7*24hrs)i.e if a permit is created on a friday and is a general permit it is valid until the following thursday midnight.
    // comment 3: when dealing with signatureFileId a worker id is submited e.g in workers in sign off, competent person etc. the workers signaturefile is copied to the minIO temp bucket and folder for the permit with a signature folder with the file and id is copied to the permit signature file id. This process should as parrallel as possible and if one fails all fails and permit creation fails
    // comment 4: create a expirey service that runs at midnight every day and checks for permits that have exceeded their days valid and changes their status to expired
    // comment 5: permits can not be created if there is no toolbox today with status 'pending closure' or 'closed'
    "valid state changes": {
        // comment 6: from the key to the value array are the only valid state changes to    
        "drafted": [
            "pending approval"
        ],
        "pending approval": [
            "opened",
            "disapproved"
        ],
        "disapproved": [
            "drafted"
        ],
        "opened": [
            "pending closure",
            "cancelled",
            "expired"
        ],
        "pending closure": [
            "closed",
            "voided"
        ],
        "cancelled": [],
        "expired": [],
        "closed": [],
        "voided": []
    },
    "get toolbox risk assessment": {
        "args": "job id",
        "return": [ // returns an array of structure below
            {
                "Id": "int", // this is job id
                "Title": "string", // this is job title
                "hazards": {
                    "Id": "int",
                    "Description": "string",
                    "control measures": [
                        {
                            "Id": "int",
                            "Description": "string"
                        }
                    ]
                }
            }
        ],
        "actions": [
            "get toolbox risk assessment for job"
        ]
    },
    "verify signoff":{
        "args":"[workerId]",// array of worker ids
        "return":"boolean",
        "actions":[
            "takes today's toolbox (must be of status ''pending closure'' or ''closed'') attendence and checks if all workers in the array are in the attendence"
        ]
    },
    "create permit": {
        //comment 7: all permit types will have their own
        "args": "all properties of the relevant permit except permit return, other permits in use,and days valid. For competent person, 'work area inspection and permit renewal' and workers the worker id is submited and do as comment 3. workers training is used as designation",
        "return": "the repective permit",
        "actions": [
            "creates a permit and links it to the relevant job",
            "if only one permit is needed for the job, the created permit should be status of pending approval",
            "if more than one permit is needed",
            "check the jobs permits and check if all the needed permits have been drafted: if not the created permit is status drafted, else if this is the last permit that is needed for the job the the created permit is given a status of pending approval, the other permits (for that job) are given a status of pending approval, and the permits are linked together ",
            "if job does not require a permit throw an error",
            "verifies signoff using verify signoff method"
        ]
    },
    "get permits by job": {
        "args": "job id",
        "return": "all permits linked to the job irrespective of permit type and status",
        "actions": [
            "get all permits linked to the job"
        ]
    },
    "get permits by status": {
        "args": "status",
        "return": "all permits with the status irrespective of permit type and job",
        "actions": [
            "get all permits with the status"
        ]
    },
    "get permits by date": {
        "args": "date",
        "return": "all permits created on the date",
        "actions": [
            "get all permits created on the date"
        ]
    },
    "approve permit": {
        "args": "permit id",
        "return": "the permit",
        "actions": [
            "check if permit is pending aproval if not throw an error",
            "check if permit is the last permit needed for the job if not throw an error",
            "check if permit is the last permit needed for the job if so change the status of the permit to opened and the other permits to opened",
            "check if permit is a general permit if so change the days valid to 7",
            "check if permit is a general permit if not change the days valid to 1"
        ]
    },
    "disapprove permit": {
        "args": "permit id",
        "return": "the permit",
        "actions": [
            "check if permit is pending aproval if not throw an error",
            "change the status of the permit to disapproved",
            "add to do comment 'notification to @site HSE added here'"
        ]
    },
    "add new worker": {
        // comment 8: only for general permits
        "args": "permit id, worker id",
        "return": "the permit",
        "actions": [
            "adds worker to sign off",
            "check if permit is opened if not throw an error",
            "check if worker is already on the permit  do nothing",
            "add worker to the permit"
        ]
    },
    "renew permit": {
        // comment 9: only for general permits
        "args": "permit id, worker id",
        "return": "the permit",
        "actions": [
            "check if permit is opened if not throw an error",
            "check if permit is a general permit if not throw an error",
            "adds new entry to 'work area inspection and permit renewal' json. Handle signature file as comment 3"
        ]
    },
    "cancel permit": {
        "args": "permit id",
        "return": "the permit",
        "actions": [
            "check if permit is opened if not throw an error",
            "change the status of the permit to cancelled",
            "add to do comment 'notification to @admin HSE, @site HSE, and @client'added here"
        ]
    },
    "return permit": {
        "args": "permit id, permit return",
        "return": "the permit",
        "actions": [
            "check if permit is opened if not throw an error",
            "copies the permit issue to permit return and addes the argument to the permit return",
            "change the status of the permit to pending closure"
        ]
    },
    "void permit": {
        "args": "permit id",
        "return": "the permit",
        "actions": [
            "check if permit is pending closure if not throw an error",
            "change the status of the permit to voided",
            "add to do comment 'notification to @admin HSE, @site HSE, and @client'added here"
        ]
    },
    "close permit": {
        "args": "permit id",
        "return": "the permit",
        "actions": [
            "check if permit is pending closure if not throw an error",
            "change the status of the permit to closed",
            "add to do comment 'notification to @admin HSE, @site HSE, and @client'added here"
        ]
    },
    "add document to permit": {
        "args": "permit id, document id",
        "return": "the permit",
        "actions": [
            "add document to permit"
        ]
    },
    "remove document from permit": {
        "args": "permit id, document id",
        "return": "the permit",
        "actions": [
            "remove document from permit"
        ]
    },
    "clear permit temp folder": {
        "args": "permit id",
        "return": "none",
        "actions": [
            "delete permit temp folder",
            "only for mermits with no other state change allowed e.g cancelled, expired, voided, closed"
        ]
    }
}