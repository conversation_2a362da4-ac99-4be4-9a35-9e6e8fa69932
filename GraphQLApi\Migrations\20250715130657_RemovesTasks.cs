﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class RemovesTasks : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TaskEquipment");

            migrationBuilder.DropTable(
                name: "TaskWorkers");

            migrationBuilder.DropTable(
                name: "Tasks");

            migrationBuilder.AddColumn<bool>(
                name: "Closed",
                table: "ControlMeasures",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "Toolboxes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    EmergencyProcedures = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    ToolboxTrainingTopics = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    ClosedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Conductor = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConductedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Attendees = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Toolboxes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ToolboxJobs",
                columns: table => new
                {
                    JobsId = table.Column<int>(type: "int", nullable: false),
                    ToolboxId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ToolboxJobs", x => new { x.JobsId, x.ToolboxId });
                    table.ForeignKey(
                        name: "FK_ToolboxJobs_Jobs_JobsId",
                        column: x => x.JobsId,
                        principalTable: "Jobs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ToolboxJobs_Toolboxes_ToolboxId",
                        column: x => x.ToolboxId,
                        principalTable: "Toolboxes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Toolboxes_Date",
                table: "Toolboxes",
                column: "Date");

            migrationBuilder.CreateIndex(
                name: "IX_Toolboxes_IsDeleted",
                table: "Toolboxes",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_Toolboxes_Status",
                table: "Toolboxes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_ToolboxJobs_ToolboxId",
                table: "ToolboxJobs",
                column: "ToolboxId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ToolboxJobs");

            migrationBuilder.DropTable(
                name: "Toolboxes");

            migrationBuilder.DropColumn(
                name: "Closed",
                table: "ControlMeasures");

            migrationBuilder.CreateTable(
                name: "Tasks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ChiefEngineerId = table.Column<int>(type: "int", nullable: true),
                    AssociatedMethodStatement = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Category = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    DueDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    InspectionStatus = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Priority = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TaskNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    TimeForCompletion = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Type = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tasks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Tasks_Workers_ChiefEngineerId",
                        column: x => x.ChiefEngineerId,
                        principalTable: "Workers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "TaskEquipment",
                columns: table => new
                {
                    EquipmentInvolvedId = table.Column<int>(type: "int", nullable: false),
                    TasksId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskEquipment", x => new { x.EquipmentInvolvedId, x.TasksId });
                    table.ForeignKey(
                        name: "FK_TaskEquipment_Equipment_EquipmentInvolvedId",
                        column: x => x.EquipmentInvolvedId,
                        principalTable: "Equipment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TaskEquipment_Tasks_TasksId",
                        column: x => x.TasksId,
                        principalTable: "Tasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TaskWorkers",
                columns: table => new
                {
                    TaskId = table.Column<int>(type: "int", nullable: false),
                    WorkersAssignedId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskWorkers", x => new { x.TaskId, x.WorkersAssignedId });
                    table.ForeignKey(
                        name: "FK_TaskWorkers_Tasks_TaskId",
                        column: x => x.TaskId,
                        principalTable: "Tasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TaskWorkers_Workers_WorkersAssignedId",
                        column: x => x.WorkersAssignedId,
                        principalTable: "Workers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TaskEquipment_TasksId",
                table: "TaskEquipment",
                column: "TasksId");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_ChiefEngineerId",
                table: "Tasks",
                column: "ChiefEngineerId");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_TaskNumber",
                table: "Tasks",
                column: "TaskNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaskWorkers_WorkersAssignedId",
                table: "TaskWorkers",
                column: "WorkersAssignedId");
        }
    }
}
