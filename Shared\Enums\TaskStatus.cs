﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Enums
{
    public enum TaskStatus
    {
        TODO,
        IN_PROGRESS,
        COMPLETED,
        CANCELLED,
        ON_HOLD
    }

    public enum TaskPriority
    {
        <PERSON>OW,
        <PERSON><PERSON>UM,
        <PERSON>IGH,
        CRITICAL
    }

    public enum InspectionStatus
    {
        NOT_REQUIRED,
        PENDING,
        IN_PROGRESS,
        COMPLETED,
        FAILED,
        REWORK_REQUIRED
    }

    public enum JobStatus
    {
        REQUESTED,
        BLOCKED,
        PENDING_APPROVAL,
        APPROVED,
        DISAPPROVED,
        FINISHED
    }

    public enum PermitType
    {
        GENERAL_WORK_PERMIT,
        HOT_WORK_PERMIT,
        CONFINED_SPACE_ENTRY_PERMIT,
        WORK_AT_HEIGHT_PERMIT,
        EXCAVATION_PERMIT
    }

    public enum PermitStatus
    {
        DRAFTED,
        PENDING_APPROVAL,
        OPENED,
        CANCELLED,
        PENDING_CLOSURE,
        CLOSED,
        VOIDED,
        D<PERSON>APPROVED,
        EXPIRED
    }

    public enum ToolboxStatus
    {
        FILLED,
        PENDING_CLOSURE,
        CLOSED
    }
}
