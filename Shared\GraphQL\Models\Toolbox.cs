using System;
using System.Collections.Generic;
using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    public class Toolbox : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public ToolboxStatus Status { get; set; } = ToolboxStatus.FILLED;
        public string EmergencyProcedures { get; set; } = string.Empty;
        public string ToolboxTrainingTopics { get; set; } = string.Empty;
        public DateTime? ClosedDate { get; set; }

        // JSON objects for conductor and conducted by
        public ToolboxConductor Conductor { get; set; } = null!;
        public ToolboxConductor? ConductedBy { get; set; }

        // JSON array for attendees
        public ICollection<ToolboxAttendee> Attendees { get; set; } = new List<ToolboxAttendee>();

        // Many-to-many relationship with Jobs
        public ICollection<Job> Jobs { get; set; } = new List<Job>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }

    public class ToolboxConductor
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
    }

    public class ToolboxAttendee
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty; // training for the worker
        public string SignatureFileId { get; set; } = string.Empty;
    }
}
