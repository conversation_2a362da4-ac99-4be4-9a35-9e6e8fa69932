using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using Shared.Enums;
using System.Text.Json;

namespace GraphQLApi.Data.Configurations
{
    public class JobConfiguration : IEntityTypeConfiguration<Job>
    {
        public void Configure(EntityTypeBuilder<Job> builder)
        {
            builder.HasKey(j => j.Id);

            builder.Property(j => j.Title)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(j => j.Description)
                .HasMaxLength(1000);

            builder.Property(j => j.Status)
                .IsRequired()
                .HasConversion<string>();

            // Configure RequiredPermits as JSON
            builder.Property(j => j.RequiredPermits)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                    v => JsonSerializer.Deserialize<ICollection<PermitType>>(v, (JsonSerializerOptions)null!) ?? new List<PermitType>())
                .HasColumnType("nvarchar(max)");

            builder.Property(j => j.TimeForCompletion)
                .HasMaxLength(50);

            builder.Property(j => j.StartDate)
                .IsRequired();

            builder.Property(j => j.DueDate);

            // Configure relationships
            builder.HasOne(j => j.Category)
                .WithMany(c => c.Jobs)
                .HasForeignKey(j => j.CategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // Use NoAction to avoid multiple cascade paths with Worker table
            builder.HasOne(j => j.RequestedBy)
                .WithMany()
                .HasForeignKey(j => j.RequestedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.BlockedBy)
                .WithMany()
                .HasForeignKey(j => j.BlockedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.ReviewedBy)
                .WithMany()
                .HasForeignKey(j => j.ReviewedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.ApprovedBy)
                .WithMany()
                .HasForeignKey(j => j.ApprovedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.FinishedBy)
                .WithMany()
                .HasForeignKey(j => j.FinishedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.ChiefEngineer)
                .WithMany()
                .HasForeignKey(j => j.ChiefEngineerId)
                .OnDelete(DeleteBehavior.NoAction);

            // Many-to-many relationship with Workers
            builder.HasMany(j => j.Workers)
                .WithMany()
                .UsingEntity("JobWorkers");

            // One-to-many relationships
            builder.HasMany(j => j.Hazards)
                .WithOne(h => h.Job)
                .HasForeignKey(h => h.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(j => j.Documents)
                .WithMany()
                .UsingEntity("JobDocuments");

            // One-to-many relationship with Permits
            builder.HasMany(j => j.Permits)
                .WithOne(p => p.Job)
                .HasForeignKey(p => p.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            // Audit fields
            builder.Property(j => j.CreatedAt)
                .IsRequired();

            builder.Property(j => j.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(j => j.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(j => j.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(j => j.DeletedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(j => j.Status);
            builder.HasIndex(j => j.StartDate);
            builder.HasIndex(j => j.DueDate);
            builder.HasIndex(j => j.ChiefEngineerId);
            builder.HasIndex(j => j.CategoryId);
        }
    }
}
