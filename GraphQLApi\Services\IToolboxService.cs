using Shared.GraphQL.Models;
using Task = System.Threading.Tasks.Task;

namespace GraphQLApi.Services
{
    public interface IToolboxService
    {
        // Get today's job risk assessment
        Task<IEnumerable<TodaysJobRiskAssessment>> GetTodaysJobRiskAssessmentAsync();

        // Create toolbox
        Task<Toolbox> CreateToolboxAsync(CreateToolboxInput input);

        // Add attendees
        Task AddAttendeesAsync(int toolboxId, IEnumerable<ToolboxAttendeeInput> attendees);

        // Summarize toolbox
        Task SummarizeToolboxAsync(int toolboxId, IEnumerable<SummarizeToolboxJobInput> jobs);

        // Add hazard
        Task AddHazardAsync(AddHazardInput input);

        // Add control measure
        Task AddControlMeasureAsync(AddControlMeasureInput input);

        // Clear permit temp folder
        Task ClearPermitTempFolderAsync(int permitId);

        // General queries
        Task<Toolbox?> GetToolboxByIdAsync(int id);
        Task<IEnumerable<Toolbox>> GetAllToolboxesAsync();
        Task<IEnumerable<Toolbox>> GetToolboxesByDateAsync(DateTime date);
        Task<Toolbox?> GetTodaysToolboxAsync();
    }

    // Input classes for service methods
    public class CreateToolboxInput
    {
        public int ConductorId { get; set; }
        public IEnumerable<CreateToolboxJobInput> Jobs { get; set; } = new List<CreateToolboxJobInput>();
        public string EmergencyProcedures { get; set; } = string.Empty;
        public string ToolboxTrainingTopics { get; set; } = string.Empty;
    }

    public class CreateToolboxJobInput
    {
        public int JobId { get; set; }
        public CreateToolboxHazardInput Hazard { get; set; } = null!;
    }

    public class CreateToolboxHazardInput
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public IEnumerable<CreateToolboxControlMeasureInput> ControlMeasures { get; set; } = new List<CreateToolboxControlMeasureInput>();
    }

    public class CreateToolboxControlMeasureInput
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class ToolboxAttendeeInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
    }

    public class SummarizeToolboxJobInput
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public SummarizeToolboxHazardInput Hazards { get; set; } = null!;
    }

    public class SummarizeToolboxHazardInput
    {
        public string Description { get; set; } = string.Empty;
        public IEnumerable<SummarizeToolboxControlMeasureInput> ControlMeasures { get; set; } = new List<SummarizeToolboxControlMeasureInput>();
    }

    public class SummarizeToolboxControlMeasureInput
    {
        public string Description { get; set; } = string.Empty;
    }

    public class AddHazardInput
    {
        public int JobId { get; set; }
        public string Description { get; set; } = string.Empty;
        public IEnumerable<string> ControlMeasures { get; set; } = new List<string>();
    }

    public class AddControlMeasureInput
    {
        public int HazardId { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    // Output classes
    public class TodaysJobRiskAssessment
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public TodaysJobHazard Hazards { get; set; } = null!;
    }

    public class TodaysJobHazard
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public IEnumerable<TodaysJobControlMeasure> ControlMeasures { get; set; } = new List<TodaysJobControlMeasure>();
    }

    public class TodaysJobControlMeasure
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
    }
}
