using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Permits;
using Shared.Enums;
using Task = System.Threading.Tasks.Task;

namespace GraphQLApi.Services
{
    public interface IPermitService
    {
        // Get toolbox risk assessment
        Task<IEnumerable<ToolboxRiskAssessment>> GetToolboxRiskAssessmentAsync(int jobId);

        // Verify signoff
        Task<bool> VerifySignoffAsync(IEnumerable<int> workerIds);

        // Create permit methods for each permit type
        Task<GeneralWorkPermit> CreateGeneralWorkPermitAsync(CreateGeneralWorkPermitInput input);
        Task<HotWorkPermit> CreateHotWorkPermitAsync(CreateHotWorkPermitInput input);
        Task<ExcavationWorkPermit> CreateExcavationWorkPermitAsync(CreateExcavationWorkPermitInput input);
        Task<WorkAtHeightPermit> CreateWorkAtHeightPermitAsync(CreateWorkAtHeightPermitInput input);
        Task<ConfinedSpacePermit> CreateConfinedSpacePermitAsync(CreateConfinedSpacePermitInput input);

        // Get permits
        Task<IEnumerable<Permit>> GetPermitsByJobAsync(int jobId);
        Task<IEnumerable<Permit>> GetPermitsByStatusAsync(PermitStatus status);
        Task<IEnumerable<Permit>> GetPermitsByDateAsync(DateTime date);

        // Permit state management
        Task<Permit> ApprovePermitAsync(int permitId);
        Task<Permit> DisapprovePermitAsync(int permitId);
        Task<Permit> CancelPermitAsync(int permitId);
        Task<Permit> ReturnPermitAsync(int permitId, PermitReturnInput permitReturn);
        Task<Permit> VoidPermitAsync(int permitId);
        Task<Permit> ClosePermitAsync(int permitId);

        // General permit specific operations
        Task<GeneralWorkPermit> AddNewWorkerAsync(int permitId, int workerId);
        Task<GeneralWorkPermit> RenewPermitAsync(int permitId, int workerId);

        // Document management
        Task<Permit> AddDocumentToPermitAsync(int permitId, int documentId);
        Task<Permit> RemoveDocumentFromPermitAsync(int permitId, int documentId);

        // Cleanup
        Task ClearPermitTempFolderAsync(int permitId);

        // General queries
        Task<Permit?> GetPermitByIdAsync(int id);
        Task<IEnumerable<Permit>> GetAllPermitsAsync();
    }

    // Input classes for service methods
    public class CreateGeneralWorkPermitInput : CreatePermitBaseInput
    {
        public string Isolation { get; set; } = string.Empty;
    }

    public class CreateHotWorkPermitInput : CreatePermitBaseInput
    {
        public string NatureOfWork { get; set; } = string.Empty;
        public string FireExtinguishers { get; set; } = string.Empty;
        public FireSafetySupervisorInput FireSafetySupervisor { get; set; } = null!;
    }

    public class CreateExcavationWorkPermitInput : CreatePermitBaseInput
    {
        public string DepthOfExcavation { get; set; } = string.Empty;
        public string ProtectionSystems { get; set; } = string.Empty;
        public string ListOfEquipmentToBeUsed { get; set; } = string.Empty;
        public string Inspections { get; set; } = string.Empty;
        public InspectionAuthorizationInput InspectionAuthorization { get; set; } = null!;
    }

    public class CreateWorkAtHeightPermitInput : CreatePermitBaseInput
    {
        public string ModeOfAccessToBeUsed { get; set; } = string.Empty;
        public string Inspections { get; set; } = string.Empty;
        public InspectionAuthorizationInput InspectionAuthorization { get; set; } = null!;
    }

    public class CreateConfinedSpacePermitInput : CreatePermitBaseInput
    {
        public bool WorkersHaveBeenTrained { get; set; }
        public string NameOfTrainingOrganization { get; set; } = string.Empty;
        public AtmosphericReadingInput TopReading { get; set; } = null!;
        public AtmosphericReadingInput MidReading { get; set; } = null!;
        public AtmosphericReadingInput BottomReading { get; set; } = null!;
        public string EmergencyGuidelines { get; set; } = string.Empty;
        public TaskObserverInput TaskObserver { get; set; } = null!;
    }

    public class CreatePermitBaseInput
    {
        public int JobId { get; set; }
        public string PTWRefNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public DateTime StartingDateTime { get; set; }
        public DateTime EndingDateTime { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Hazards { get; set; } = string.Empty;
        public string PrecautionsRequired { get; set; } = string.Empty;
        public string PPE { get; set; } = string.Empty;
        public PermitIssuerInput PermitIssuer { get; set; } = null!;
        public SignOffInput SignOff { get; set; } = null!;
    }

    public class PermitIssuerInput
    {
        public List<CompetentPersonInput> CompetentPersons { get; set; } = new();
        public List<AuthorisedPersonInput> AuthorisedPersons { get; set; } = new();
    }

    public class PermitReturnInput
    {
        public List<CompetentPersonInput> CompetentPersons { get; set; } = new();
        public List<AuthorisedPersonInput> AuthorisedPersons { get; set; } = new();
    }

    public class SignOffInput
    {
        public DateTime DateTime { get; set; }
        public List<PermitWorkerInput> Workers { get; set; } = new();
    }

    public class CompetentPersonInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class AuthorisedPersonInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class PermitWorkerInput
    {
        public int WorkerId { get; set; }
        public string Designation { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class FireSafetySupervisorInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class InspectionAuthorizationInput
    {
        public string NameOfInspector { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public DateTime DateOfInspection { get; set; }
        public string Comments { get; set; } = string.Empty;
    }

    public class AtmosphericReadingInput
    {
        public string Oxygen { get; set; } = string.Empty;
        public string Explosive { get; set; } = string.Empty;
        public string Toxic { get; set; } = string.Empty;
        public string Co2 { get; set; } = string.Empty;
    }

    public class TaskObserverInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    // Output classes
    public class ToolboxRiskAssessment
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public ToolboxRiskAssessmentHazard Hazards { get; set; } = null!;
    }

    public class ToolboxRiskAssessmentHazard
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public IEnumerable<ToolboxRiskAssessmentControlMeasure> ControlMeasures { get; set; } = new List<ToolboxRiskAssessmentControlMeasure>();
    }

    public class ToolboxRiskAssessmentControlMeasure
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
    }
}
