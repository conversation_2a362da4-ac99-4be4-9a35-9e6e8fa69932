using System.ComponentModel.DataAnnotations;
using Shared.Enums;

namespace Shared.GraphQL.Models.Permits
{
    /// <summary>
    /// Excavation Work Permit - derived from base Permit class
    /// </summary>
    public class ExcavationWorkPermit : Permit
    {
        public ExcavationWorkPermit()
        {
            PermitType = PermitType.EXCAVATION_PERMIT;
        }

        [StringLength(200)]
        public string DepthOfExcavation { get; set; } = string.Empty;

        [StringLength(1000)]
        public string ProtectionSystems { get; set; } = string.Empty;

        [StringLength(1000)]
        public string ListOfEquipmentToBeUsed { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Inspections { get; set; } = string.Empty;

        // JSON object for inspection authorization
        public InspectionAuthorization InspectionAuthorization { get; set; } = null!;
    }
}
