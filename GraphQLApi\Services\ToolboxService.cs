using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Services
{
    public class ToolboxService : IToolboxService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly ILogger<ToolboxService> _logger;

        public ToolboxService(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            ILogger<ToolboxService> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _logger = logger;
        }

        public async Task<IEnumerable<TodaysJobRiskAssessment>> GetTodaysJobRiskAssessmentAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var today = DateTime.Today;
            var approvedJobs = await context.Jobs
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .Where(j => j.Status == JobStatus.APPROVED && j.StartDate.Date == today)
                .ToListAsync();

            var result = new List<TodaysJobRiskAssessment>();
            
            foreach (var job in approvedJobs)
            {
                foreach (var hazard in job.Hazards)
                {
                    result.Add(new TodaysJobRiskAssessment
                    {
                        Id = job.Id,
                        Title = job.Title,
                        Hazards = new TodaysJobHazard
                        {
                            Id = hazard.Id,
                            Description = hazard.Description,
                            ControlMeasures = hazard.ControlMeasures.Select(cm => new TodaysJobControlMeasure
                            {
                                Id = cm.Id,
                                Description = cm.Description
                            })
                        }
                    });
                }
            }

            return result;
        }

        public async Task<Toolbox> CreateToolboxAsync(CreateToolboxInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            // Validate only one toolbox per day
            var today = DateTime.Today;
            var existingToolbox = await context.Toolboxes
                .FirstOrDefaultAsync(t => t.Date.Date == today);
            
            if (existingToolbox != null)
                throw new InvalidOperationException("Only one toolbox is allowed per day");

            // Get conductor information
            var conductor = await context.Workers
                .FirstOrDefaultAsync(w => w.Id == input.ConductorId);
            
            if (conductor == null)
                throw new ArgumentException($"Worker with ID {input.ConductorId} not found");

            // Get jobs
            var jobIds = input.Jobs.Select(j => j.JobId).ToList();
            var jobs = await context.Jobs
                .Where(j => jobIds.Contains(j.Id))
                .ToListAsync();

            // Copy signature file to temp bucket
            string conductorSignatureFileId = string.Empty;
            if (conductor.SignatureFileId.HasValue)
            {
                conductorSignatureFileId = await CopySignatureToTempAsync(conductor.SignatureFileId.Value, today);
            }

            var toolbox = new Toolbox
            {
                Date = today,
                Status = ToolboxStatus.FILLED,
                EmergencyProcedures = input.EmergencyProcedures,
                ToolboxTrainingTopics = input.ToolboxTrainingTopics,
                Conductor = new ToolboxConductor
                {
                    WorkerId = conductor.Id,
                    Name = conductor.Name,
                    SignatureFileId = conductorSignatureFileId
                },
                Jobs = jobs
            };

            // Update control measures and hazards based on input
            foreach (var jobInput in input.Jobs)
            {
                var hazard = await context.Hazards
                    .Include(h => h.ControlMeasures)
                    .FirstOrDefaultAsync(h => h.Id == jobInput.Hazard.Id);
                
                if (hazard != null)
                {
                    // Update hazard description if provided
                    if (!string.IsNullOrEmpty(jobInput.Hazard.Description))
                    {
                        hazard.Description = jobInput.Hazard.Description;
                    }

                    // Update control measures
                    foreach (var cmInput in jobInput.Hazard.ControlMeasures)
                    {
                        var controlMeasure = hazard.ControlMeasures
                            .FirstOrDefault(cm => cm.Id == cmInput.Id);
                        
                        if (controlMeasure != null && !string.IsNullOrEmpty(cmInput.Description))
                        {
                            controlMeasure.Description = cmInput.Description;
                            controlMeasure.Closed = false; // Set closed to false as per requirements
                        }
                    }
                }
            }

            context.Toolboxes.Add(toolbox);
            await context.SaveChangesAsync();

            return toolbox;
        }

        public async Task AddAttendeesAsync(int toolboxId, IEnumerable<ToolboxAttendeeInput> attendees)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var toolbox = await context.Toolboxes
                .FirstOrDefaultAsync(t => t.Id == toolboxId);
            
            if (toolbox == null)
                throw new ArgumentException($"Toolbox with ID {toolboxId} not found");

            // Validate state transition
            if (toolbox.Status != ToolboxStatus.FILLED)
                throw new InvalidOperationException($"Cannot add attendees to toolbox with status {toolbox.Status}");

            var attendeeList = new List<ToolboxAttendee>();
            
            // Process attendees and copy signature files
            foreach (var attendeeInput in attendees)
            {
                var worker = await context.Workers
                    .FirstOrDefaultAsync(w => w.Id == attendeeInput.WorkerId);
                
                if (worker == null)
                    throw new ArgumentException($"Worker with ID {attendeeInput.WorkerId} not found");

                string signatureFileId = string.Empty;
                if (worker.SignatureFileId.HasValue)
                {
                    signatureFileId = await CopySignatureToTempAsync(worker.SignatureFileId.Value, toolbox.Date);
                }

                attendeeList.Add(new ToolboxAttendee
                {
                    WorkerId = attendeeInput.WorkerId,
                    Name = attendeeInput.Name,
                    Designation = attendeeInput.Designation,
                    SignatureFileId = signatureFileId
                });
            }

            toolbox.Attendees = attendeeList;
            toolbox.Status = ToolboxStatus.PENDING_CLOSURE;

            await context.SaveChangesAsync();
        }

        private async Task<string> CopySignatureToTempAsync(int sourceFileId, DateTime toolboxDate)
        {
            try
            {
                // Get source file metadata
                await using var context = await _contextFactory.CreateDbContextAsync();
                var sourceFile = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == sourceFileId);
                
                if (sourceFile == null)
                    return string.Empty;

                // Create temp folder path for the toolbox
                var tempFolderPath = $"toolbox_{toolboxDate:yyyyMMdd}/signatures";
                
                // Copy file to temp bucket
                var tempFileName = $"{sourceFile.FileName}_{Guid.NewGuid()}";
                
                // This is a simplified implementation - in reality, you'd need to implement
                // the actual file copying logic using MinIO service
                // For now, return the original file ID as string
                return sourceFileId.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy signature file {FileId} to temp bucket", sourceFileId);
                throw new InvalidOperationException("Failed to copy signature file", ex);
            }
        }

        public async Task SummarizeToolboxAsync(int toolboxId, IEnumerable<SummarizeToolboxJobInput> jobs)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var toolbox = await context.Toolboxes
                .Include(t => t.Jobs)
                .FirstOrDefaultAsync(t => t.Id == toolboxId);
            
            if (toolbox == null)
                throw new ArgumentException($"Toolbox with ID {toolboxId} not found");

            foreach (var jobInput in jobs)
            {
                var job = toolbox.Jobs.FirstOrDefault(j => j.Id == jobInput.Id);
                if (job != null)
                {
                    // Add new hazards to job
                    var newHazard = new Hazard
                    {
                        Description = jobInput.Hazards.Description,
                        JobId = job.Id
                    };

                    // Add control measures to the hazard
                    foreach (var cmInput in jobInput.Hazards.ControlMeasures)
                    {
                        newHazard.ControlMeasures.Add(new ControlMeasure
                        {
                            Description = cmInput.Description,
                            Closed = true // Mark as closed as per requirements
                        });
                    }

                    context.Hazards.Add(newHazard);
                }
            }

            await context.SaveChangesAsync();
        }

        public async Task AddHazardAsync(AddHazardInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var job = await context.Jobs
                .FirstOrDefaultAsync(j => j.Id == input.JobId);
            
            if (job == null)
                throw new ArgumentException($"Job with ID {input.JobId} not found");

            var hazard = new Hazard
            {
                Description = input.Description,
                JobId = input.JobId
            };

            // Create associated control measures
            foreach (var cmDescription in input.ControlMeasures)
            {
                hazard.ControlMeasures.Add(new ControlMeasure
                {
                    Description = cmDescription
                });
            }

            context.Hazards.Add(hazard);
            await context.SaveChangesAsync();
        }

        public async Task AddControlMeasureAsync(AddControlMeasureInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var hazard = await context.Hazards
                .FirstOrDefaultAsync(h => h.Id == input.HazardId);
            
            if (hazard == null)
                throw new ArgumentException($"Hazard with ID {input.HazardId} not found");

            var controlMeasure = new ControlMeasure
            {
                Description = input.Description,
                HazardId = input.HazardId
            };

            context.ControlMeasures.Add(controlMeasure);
            await context.SaveChangesAsync();
        }

        public async Task ClearPermitTempFolderAsync(int permitId)
        {
            try
            {
                // This would implement the logic to delete permit temp folder
                // Only for permits with no other state change allowed (e.g., closed)
                await using var context = await _contextFactory.CreateDbContextAsync();
                
                // Check if permit exists and is in a final state
                // Implementation would depend on permit structure
                
                _logger.LogInformation("Cleared temp folder for permit {PermitId}", permitId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear temp folder for permit {PermitId}", permitId);
                throw;
            }
        }

        public async Task<Toolbox?> GetToolboxByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Toolboxes
                .Include(t => t.Jobs)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<IEnumerable<Toolbox>> GetAllToolboxesAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Toolboxes
                .Include(t => t.Jobs)
                .ToListAsync();
        }

        public async Task<IEnumerable<Toolbox>> GetToolboxesByDateAsync(DateTime date)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Toolboxes
                .Include(t => t.Jobs)
                .Where(t => t.Date.Date == date.Date)
                .ToListAsync();
        }

        public async Task<Toolbox?> GetTodaysToolboxAsync()
        {
            var today = DateTime.Today;
            var toolboxes = await GetToolboxesByDateAsync(today);
            return toolboxes.FirstOrDefault();
        }
    }
}
