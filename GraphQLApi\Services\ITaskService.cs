// using Shared.GraphQL.Models;
// using Task = Shared.GraphQL.Models.Task;

// namespace GraphQLApi.Services
// {
//     public interface ITaskService
//     {
//         Task<IEnumerable<Task>> GetAllTasksAsync();
//         Task<Task?> GetTaskByIdAsync(int id);
//         Task<Task> CreateTaskAsync(Task task);
//         Task<Task?> UpdateTaskAsync(int id, Task task);
//         Task<bool> DeleteTaskAsync(int id);
//         Task<IEnumerable<Task>> GetTasksByWorkerIdAsync(int workerId);
//         Task<IEnumerable<Task>> GetTasksByChiefEngineerIdAsync(int chiefEngineerId);
//         Task<IEnumerable<Task>> GetTasksByStatusAsync(Shared.Enums.TaskStatus status);
//         Task<IEnumerable<Task>> GetTasksByPriorityAsync(Shared.Enums.TaskPriority priority);
//         Task<string> GenerateTaskNumberAsync();
//     }
// }
