using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Permits;
using Shared.Enums;
using Task = System.Threading.Tasks.Task;

namespace GraphQLApi.Services
{
    public class PermitService : IPermitService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly IToolboxService _toolboxService;
        private readonly ILogger<PermitService> _logger;

        // Valid state transitions as defined in permit_methods.jsonc
        private readonly Dictionary<PermitStatus, List<PermitStatus>> _validStateTransitions = new()
        {
            { PermitStatus.DRAFTED, new List<PermitStatus> { PermitStatus.PENDING_APPROVAL } },
            { PermitStatus.PENDING_APPROVAL, new List<PermitStatus> { PermitStatus.OPENED, PermitStatus.DISAPPROVED } },
            { PermitStatus.DISAPPROVED, new List<PermitStatus> { PermitStatus.DRAFTED } },
            { PermitStatus.OPENED, new List<PermitStatus> { PermitStatus.PENDING_CLOSURE, PermitStatus.CANCELLED, PermitStatus.EXPIRED } },
            { PermitStatus.PENDING_CLOSURE, new List<PermitStatus> { PermitStatus.CLOSED, PermitStatus.VOIDED } },
            { PermitStatus.CANCELLED, new List<PermitStatus>() },
            { PermitStatus.EXPIRED, new List<PermitStatus>() },
            { PermitStatus.CLOSED, new List<PermitStatus>() },
            { PermitStatus.VOIDED, new List<PermitStatus>() }
        };

        public PermitService(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            IToolboxService toolboxService,
            ILogger<PermitService> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _toolboxService = toolboxService;
            _logger = logger;
        }

        public async Task<IEnumerable<ToolboxRiskAssessment>> GetToolboxRiskAssessmentAsync(int jobId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var job = await context.Jobs
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            var result = new List<ToolboxRiskAssessment>();
            
            foreach (var hazard in job.Hazards)
            {
                result.Add(new ToolboxRiskAssessment
                {
                    Id = job.Id,
                    Title = job.Title,
                    Hazards = new ToolboxRiskAssessmentHazard
                    {
                        Id = hazard.Id,
                        Description = hazard.Description,
                        ControlMeasures = hazard.ControlMeasures.Select(cm => new ToolboxRiskAssessmentControlMeasure
                        {
                            Id = cm.Id,
                            Description = cm.Description
                        })
                    }
                });
            }

            return result;
        }

        public async Task<bool> VerifySignoffAsync(IEnumerable<int> workerIds)
        {
            var todaysToolbox = await _toolboxService.GetTodaysToolboxAsync();
            
            if (todaysToolbox == null)
                return false;

            // Check if toolbox is in correct status
            if (todaysToolbox.Status != ToolboxStatus.PENDING_CLOSURE && todaysToolbox.Status != ToolboxStatus.CLOSED)
                return false;

            // Check if all workers are in the attendance
            var attendeeWorkerIds = todaysToolbox.Attendees.Select(a => a.WorkerId).ToHashSet();
            
            return workerIds.All(workerId => attendeeWorkerIds.Contains(workerId));
        }

        public async Task<GeneralWorkPermit> CreateGeneralWorkPermitAsync(CreateGeneralWorkPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            // Validate job exists and requires permits
            var job = await context.Jobs
                .Include(j => j.Permits)
                .FirstOrDefaultAsync(j => j.Id == input.JobId);
            
            if (job == null)
                throw new ArgumentException($"Job with ID {input.JobId} not found");

            if (!job.RequiredPermits.Contains(PermitType.GENERAL_WORK_PERMIT))
                throw new InvalidOperationException("Job does not require a General Work Permit");

            // Verify signoff
            var workerIds = input.SignOff.Workers.Select(w => w.WorkerId);
            if (!await VerifySignoffAsync(workerIds))
                throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

            // Copy signature files to temp bucket
            var processedPermitIssuer = await ProcessPermitIssuerSignatures(input.PermitIssuer, input.JobId);
            var processedSignOff = await ProcessSignOffSignatures(input.SignOff, input.JobId);

            var permit = new GeneralWorkPermit
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                StartingDateTime = input.StartingDateTime,
                EndingDateTime = input.EndingDateTime,
                Description = input.Description,
                Location = input.Location,
                Hazards = input.Hazards,
                PrecautionsRequired = input.PrecautionsRequired,
                PPE = input.PPE,
                Isolation = input.Isolation,
                PermitIssuer = processedPermitIssuer,
                SignOff = processedSignOff,
                Status = DetermineInitialPermitStatus(job)
            };

            // Link permits if this is the last required permit
            if (permit.Status == PermitStatus.PENDING_APPROVAL)
            {
                await LinkRelatedPermits(context, job, permit);
            }

            context.GeneralWorkPermits.Add(permit);
            await context.SaveChangesAsync();

            return permit;
        }

        public async Task<HotWorkPermit> CreateHotWorkPermitAsync(CreateHotWorkPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var job = await context.Jobs
                .Include(j => j.Permits)
                .FirstOrDefaultAsync(j => j.Id == input.JobId);
            
            if (job == null)
                throw new ArgumentException($"Job with ID {input.JobId} not found");

            if (!job.RequiredPermits.Contains(PermitType.HOT_WORK_PERMIT))
                throw new InvalidOperationException("Job does not require a Hot Work Permit");

            var workerIds = input.SignOff.Workers.Select(w => w.WorkerId);
            if (!await VerifySignoffAsync(workerIds))
                throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

            var processedPermitIssuer = await ProcessPermitIssuerSignatures(input.PermitIssuer, input.JobId);
            var processedSignOff = await ProcessSignOffSignatures(input.SignOff, input.JobId);
            var processedFireSafetySupervisor = await ProcessFireSafetySupervisorSignature(input.FireSafetySupervisor, input.JobId);

            var permit = new HotWorkPermit
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                StartingDateTime = input.StartingDateTime,
                EndingDateTime = input.EndingDateTime,
                Description = input.Description,
                Location = input.Location,
                Hazards = input.Hazards,
                PrecautionsRequired = input.PrecautionsRequired,
                PPE = input.PPE,
                NatureOfWork = input.NatureOfWork,
                FireExtinguishers = input.FireExtinguishers,
                FireSafetySupervisor = processedFireSafetySupervisor,
                PermitIssuer = processedPermitIssuer,
                SignOff = processedSignOff,
                Status = DetermineInitialPermitStatus(job)
            };

            if (permit.Status == PermitStatus.PENDING_APPROVAL)
            {
                await LinkRelatedPermits(context, job, permit);
            }

            context.HotWorkPermits.Add(permit);
            await context.SaveChangesAsync();

            return permit;
        }

        private static PermitStatus DetermineInitialPermitStatus(Job job)
        {
            var requiredPermitTypes = job.RequiredPermits.ToList();
            var existingPermitTypes = job.Permits.Select(p => p.PermitType).ToList();

            // If only one permit is needed for the job
            if (requiredPermitTypes.Count == 1)
            {
                return PermitStatus.PENDING_APPROVAL;
            }

            // If more than one permit is needed
            // Check if all needed permits have been drafted
            var missingPermitTypes = requiredPermitTypes.Except(existingPermitTypes).ToList();
            
            // If this is the last permit needed
            if (missingPermitTypes.Count == 1)
            {
                return PermitStatus.PENDING_APPROVAL;
            }

            // Otherwise, status is drafted
            return PermitStatus.DRAFTED;
        }

        private static Task LinkRelatedPermits(AppDbContext context, Job job, Permit newPermit)
        {
            // Get all other permits for this job
            var otherPermits = job.Permits.Where(p => p.Id != newPermit.Id).ToList();
            
            // Set all permits to pending approval and link them
            foreach (var permit in otherPermits)
            {
                permit.Status = PermitStatus.PENDING_APPROVAL;
                newPermit.OtherPermitsInUse.Add(permit);
                permit.OtherPermitsInUse.Add(newPermit);
            }

            return System.Threading.Tasks.Task.CompletedTask;

        }

        private async Task<PermitIssuer> ProcessPermitIssuerSignatures(PermitIssuerInput input, int jobId)
        {
            var processedCompetentPersons = new List<CompetentPerson>();
            var processedAuthorisedPersons = new List<AuthorisedPerson>();

            foreach (var cp in input.CompetentPersons)
            {
                var signatureFileId = await CopySignatureToTempAsync(int.Parse(cp.SignatureFileId), jobId);
                processedCompetentPersons.Add(new CompetentPerson
                {
                    WorkerId = cp.WorkerId,
                    Name = cp.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = cp.SignedAt
                });
            }

            foreach (var ap in input.AuthorisedPersons)
            {
                var signatureFileId = await CopySignatureToTempAsync(int.Parse(ap.SignatureFileId), jobId);
                processedAuthorisedPersons.Add(new AuthorisedPerson
                {
                    WorkerId = ap.WorkerId,
                    Name = ap.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = ap.SignedAt
                });
            }

            return new PermitIssuer
            {
                CompetentPersons = processedCompetentPersons,
                AuthorisedPersons = processedAuthorisedPersons
            };
        }

        private async Task<SignOff> ProcessSignOffSignatures(SignOffInput input, int jobId)
        {
            var processedWorkers = new List<PermitWorker>();

            foreach (var worker in input.Workers)
            {
                var signatureFileId = await CopySignatureToTempAsync(int.Parse(worker.SignatureFileId), jobId);
                processedWorkers.Add(new PermitWorker
                {
                    WorkerId = worker.WorkerId,
                    Designation = worker.Designation,
                    Name = worker.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = worker.SignedAt
                });
            }

            return new SignOff
            {
                DateTime = input.DateTime,
                Workers = processedWorkers
            };
        }

        private async Task<FireSafetySupervisor> ProcessFireSafetySupervisorSignature(FireSafetySupervisorInput input, int jobId)
        {
            var signatureFileId = await CopySignatureToTempAsync(int.Parse(input.SignatureFileId), jobId);
            
            return new FireSafetySupervisor
            {
                WorkerId = input.WorkerId,
                Name = input.Name,
                SignatureFileId = signatureFileId,
                SignedAt = input.SignedAt
            };
        }

        private async Task<string> CopySignatureToTempAsync(int sourceFileId, int jobId)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var sourceFile = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == sourceFileId);
                
                if (sourceFile == null)
                    return string.Empty;

                var tempFolderPath = $"permit_{jobId}/signatures";
                var tempFileName = $"{sourceFile.FileName}_{Guid.NewGuid()}";
                
                // This is a simplified implementation - in reality, you'd implement
                // the actual file copying logic using MinIO service
                return sourceFileId.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy signature file {FileId} to temp bucket", sourceFileId);
                throw new InvalidOperationException("Failed to copy signature file", ex);
            }
        }

        public async Task<ExcavationWorkPermit> CreateExcavationWorkPermitAsync(CreateExcavationWorkPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Permits)
                .FirstOrDefaultAsync(j => j.Id == input.JobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {input.JobId} not found");

            if (!job.RequiredPermits.Contains(PermitType.EXCAVATION_PERMIT))
                throw new InvalidOperationException("Job does not require an Excavation Work Permit");

            var workerIds = input.SignOff.Workers.Select(w => w.WorkerId);
            if (!await VerifySignoffAsync(workerIds))
                throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

            var processedPermitIssuer = await ProcessPermitIssuerSignatures(input.PermitIssuer, input.JobId);
            var processedSignOff = await ProcessSignOffSignatures(input.SignOff, input.JobId);
            var processedInspectionAuth = await ProcessInspectionAuthorizationSignature(input.InspectionAuthorization, input.JobId);

            var permit = new ExcavationWorkPermit
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                StartingDateTime = input.StartingDateTime,
                EndingDateTime = input.EndingDateTime,
                Description = input.Description,
                Location = input.Location,
                Hazards = input.Hazards,
                PrecautionsRequired = input.PrecautionsRequired,
                PPE = input.PPE,
                DepthOfExcavation = input.DepthOfExcavation,
                ProtectionSystems = input.ProtectionSystems,
                ListOfEquipmentToBeUsed = input.ListOfEquipmentToBeUsed,
                Inspections = input.Inspections,
                InspectionAuthorization = processedInspectionAuth,
                PermitIssuer = processedPermitIssuer,
                SignOff = processedSignOff,
                Status = DetermineInitialPermitStatus(job)
            };

            if (permit.Status == PermitStatus.PENDING_APPROVAL)
            {
                await LinkRelatedPermits(context, job, permit);
            }

            context.ExcavationWorkPermits.Add(permit);
            await context.SaveChangesAsync();

            return permit;
        }

        public async Task<WorkAtHeightPermit> CreateWorkAtHeightPermitAsync(CreateWorkAtHeightPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Permits)
                .FirstOrDefaultAsync(j => j.Id == input.JobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {input.JobId} not found");

            if (!job.RequiredPermits.Contains(PermitType.WORK_AT_HEIGHT_PERMIT))
                throw new InvalidOperationException("Job does not require a Work at Height Permit");

            var workerIds = input.SignOff.Workers.Select(w => w.WorkerId);
            if (!await VerifySignoffAsync(workerIds))
                throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

            var processedPermitIssuer = await ProcessPermitIssuerSignatures(input.PermitIssuer, input.JobId);
            var processedSignOff = await ProcessSignOffSignatures(input.SignOff, input.JobId);
            var processedInspectionAuth = await ProcessInspectionAuthorizationSignature(input.InspectionAuthorization, input.JobId);

            var permit = new WorkAtHeightPermit
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                StartingDateTime = input.StartingDateTime,
                EndingDateTime = input.EndingDateTime,
                Description = input.Description,
                Location = input.Location,
                Hazards = input.Hazards,
                PrecautionsRequired = input.PrecautionsRequired,
                PPE = input.PPE,
                ModeOfAccessToBeUsed = input.ModeOfAccessToBeUsed,
                Inspections = input.Inspections,
                InspectionAuthorization = processedInspectionAuth,
                PermitIssuer = processedPermitIssuer,
                SignOff = processedSignOff,
                Status = DetermineInitialPermitStatus(job)
            };

            if (permit.Status == PermitStatus.PENDING_APPROVAL)
            {
                await LinkRelatedPermits(context, job, permit);
            }

            context.WorkAtHeightPermits.Add(permit);
            await context.SaveChangesAsync();

            return permit;
        }

        public async Task<ConfinedSpacePermit> CreateConfinedSpacePermitAsync(CreateConfinedSpacePermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Permits)
                .FirstOrDefaultAsync(j => j.Id == input.JobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {input.JobId} not found");

            if (!job.RequiredPermits.Contains(PermitType.CONFINED_SPACE_ENTRY_PERMIT))
                throw new InvalidOperationException("Job does not require a Confined Space Entry Permit");

            var workerIds = input.SignOff.Workers.Select(w => w.WorkerId);
            if (!await VerifySignoffAsync(workerIds))
                throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

            var processedPermitIssuer = await ProcessPermitIssuerSignatures(input.PermitIssuer, input.JobId);
            var processedSignOff = await ProcessSignOffSignatures(input.SignOff, input.JobId);
            var processedTaskObserver = await ProcessTaskObserverSignature(input.TaskObserver, input.JobId);

            var permit = new ConfinedSpacePermit
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                StartingDateTime = input.StartingDateTime,
                EndingDateTime = input.EndingDateTime,
                Description = input.Description,
                Location = input.Location,
                Hazards = input.Hazards,
                PrecautionsRequired = input.PrecautionsRequired,
                PPE = input.PPE,
                WorkersHaveBeenTrained = input.WorkersHaveBeenTrained,
                NameOfTrainingOrganization = input.NameOfTrainingOrganization,
                TopReading = new AtmosphericReading
                {
                    Oxygen = input.TopReading.Oxygen,
                    Explosive = input.TopReading.Explosive,
                    Toxic = input.TopReading.Toxic,
                    Co2 = input.TopReading.Co2
                },
                MidReading = new AtmosphericReading
                {
                    Oxygen = input.MidReading.Oxygen,
                    Explosive = input.MidReading.Explosive,
                    Toxic = input.MidReading.Toxic,
                    Co2 = input.MidReading.Co2
                },
                BottomReading = new AtmosphericReading
                {
                    Oxygen = input.BottomReading.Oxygen,
                    Explosive = input.BottomReading.Explosive,
                    Toxic = input.BottomReading.Toxic,
                    Co2 = input.BottomReading.Co2
                },
                EmergencyGuidelines = input.EmergencyGuidelines,
                TaskObserver = processedTaskObserver,
                PermitIssuer = processedPermitIssuer,
                SignOff = processedSignOff,
                Status = DetermineInitialPermitStatus(job)
            };

            if (permit.Status == PermitStatus.PENDING_APPROVAL)
            {
                await LinkRelatedPermits(context, job, permit);
            }

            context.ConfinedSpacePermits.Add(permit);
            await context.SaveChangesAsync();

            return permit;
        }

        private static Task<InspectionAuthorization> ProcessInspectionAuthorizationSignature(InspectionAuthorizationInput input, int jobId)
        {
            return System.Threading.Tasks.Task.FromResult(new InspectionAuthorization
            {
                NameOfInspector = input.NameOfInspector,
                Designation = input.Designation,
                DateOfInspection = input.DateOfInspection,
                Comments = input.Comments
            });
        }

        private async Task<TaskObserver> ProcessTaskObserverSignature(TaskObserverInput input, int jobId)
        {
            var signatureFileId = await CopySignatureToTempAsync(int.Parse(input.SignatureFileId), jobId);

            return new TaskObserver
            {
                WorkerId = input.WorkerId,
                Name = input.Name,
                SignatureFileId = signatureFileId,
                SignedAt = input.SignedAt
            };
        }

        public async Task<IEnumerable<Permit>> GetPermitsByJobAsync(int jobId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Where(p => p.JobId == jobId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Permit>> GetPermitsByStatusAsync(PermitStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Where(p => p.Status == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<Permit>> GetPermitsByDateAsync(DateTime date)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Where(p => p.CreatedAt.Date == date.Date)
                .ToListAsync();
        }

        public async Task<Permit> ApprovePermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .Include(p => p.Job)
                .Include(p => p.OtherPermitsInUse)
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.OPENED);

            // Check if permit is the last permit needed for the job
            var job = permit.Job;
            var allJobPermits = await context.Permits
                .Where(p => p.JobId == job.Id)
                .ToListAsync();

            var requiredPermitTypes = job.RequiredPermits.ToHashSet();
            var existingPermitTypes = allJobPermits.Select(p => p.PermitType).ToHashSet();

            if (!requiredPermitTypes.SetEquals(existingPermitTypes))
                throw new InvalidOperationException("Cannot approve permit: not all required permits have been created for this job");

            // Set status to opened and configure days valid
            permit.Status = PermitStatus.OPENED;

            if (permit.PermitType == PermitType.GENERAL_WORK_PERMIT)
            {
                permit.DaysValid = 7;
            }
            else
            {
                permit.DaysValid = 1;
            }

            // Set all other permits for this job to opened
            foreach (var otherPermit in permit.OtherPermitsInUse)
            {
                otherPermit.Status = PermitStatus.OPENED;
                if (otherPermit.PermitType == PermitType.GENERAL_WORK_PERMIT)
                {
                    otherPermit.DaysValid = 7;
                }
                else
                {
                    otherPermit.DaysValid = 1;
                }
            }

            await context.SaveChangesAsync();
            return permit;
        }

        public async Task<Permit> DisapprovePermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.DISAPPROVED);

            permit.Status = PermitStatus.DISAPPROVED;

            await context.SaveChangesAsync();

            // TODO: notification to @site HSE added here

            return permit;
        }

        public async Task<Permit> CancelPermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.CANCELLED);

            permit.Status = PermitStatus.CANCELLED;

            await context.SaveChangesAsync();

            // TODO: notification to @admin HSE, @site HSE, and @client added here

            return permit;
        }

        public async Task<Permit> ReturnPermitAsync(int permitId, PermitReturnInput permitReturn)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.PENDING_CLOSURE);

            // Process permit return signatures
            var processedPermitReturn = await ProcessPermitReturnSignatures(permitReturn, permitId);

            // Copy permit issuer to permit return and add the argument
            permit.PermitReturn = new PermitReturn
            {
                CompetentPersons = permit.PermitIssuer.CompetentPersons.Concat(processedPermitReturn.CompetentPersons).ToList(),
                AuthorisedPersons = permit.PermitIssuer.AuthorisedPersons.Concat(processedPermitReturn.AuthorisedPersons).ToList()
            };

            permit.Status = PermitStatus.PENDING_CLOSURE;

            await context.SaveChangesAsync();

            return permit;
        }

        public async Task<Permit> VoidPermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.VOIDED);

            permit.Status = PermitStatus.VOIDED;

            await context.SaveChangesAsync();

            // TODO: notification to @admin HSE, @site HSE, and @client added here

            return permit;
        }

        public async Task<Permit> ClosePermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.CLOSED);

            permit.Status = PermitStatus.CLOSED;

            await context.SaveChangesAsync();

            // TODO: notification to @admin HSE, @site HSE, and @client added here

            return permit;
        }

        public async Task<GeneralWorkPermit> AddNewWorkerAsync(int permitId, int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.GeneralWorkPermits
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"General Work Permit with ID {permitId} not found");

            if (permit.Status != PermitStatus.OPENED)
                throw new InvalidOperationException("Can only add workers to opened permits");

            // Check if worker is already on the permit
            if (permit.SignOff.Workers.Any(w => w.WorkerId == workerId))
                return permit; // Do nothing if worker already exists

            // Get worker information
            var worker = await context.Workers
                .FirstOrDefaultAsync(w => w.Id == workerId);

            if (worker == null)
                throw new ArgumentException($"Worker with ID {workerId} not found");

            // Copy worker's signature to temp bucket
            string signatureFileId = string.Empty;
            if (worker.SignatureFileId.HasValue)
            {
                signatureFileId = await CopySignatureToTempAsync(worker.SignatureFileId.Value, permitId);
            }

            // Add worker to sign off
            permit.SignOff.Workers.Add(new PermitWorker
            {
                WorkerId = workerId,
                Name = worker.Name,
                Designation = "Worker", // Default designation
                SignatureFileId = signatureFileId,
                SignedAt = DateTime.UtcNow
            });

            await context.SaveChangesAsync();

            return permit;
        }

        public async Task<GeneralWorkPermit> RenewPermitAsync(int permitId, int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.GeneralWorkPermits
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"General Work Permit with ID {permitId} not found");

            if (permit.Status != PermitStatus.OPENED)
                throw new InvalidOperationException("Can only renew opened permits");

            if (permit.PermitType != PermitType.GENERAL_WORK_PERMIT)
                throw new InvalidOperationException("Only general permits can be renewed");

            // Get worker information
            var worker = await context.Workers
                .FirstOrDefaultAsync(w => w.Id == workerId);

            if (worker == null)
                throw new ArgumentException($"Worker with ID {workerId} not found");

            // Copy worker's signature to temp bucket
            string signatureFileId = string.Empty;
            if (worker.SignatureFileId.HasValue)
            {
                signatureFileId = await CopySignatureToTempAsync(worker.SignatureFileId.Value, permitId);
            }

            // Add new entry to work area inspection and permit renewal
            permit.WorkAreaInspectionAndPermitRenewal.Add(new WorkAreaInspection
            {
                Name = worker.Name,
                SignatureFileId = signatureFileId,
                SignedAt = DateTime.UtcNow,
                Comments = "Permit renewed"
            });

            await context.SaveChangesAsync();

            return permit;
        }

        public async Task<Permit> AddDocumentToPermitAsync(int permitId, int documentId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .Include(p => p.Documents)
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            var document = await context.DocumentFiles
                .FirstOrDefaultAsync(d => d.Id == documentId);

            if (document == null)
                throw new ArgumentException($"Document with ID {documentId} not found");

            if (!permit.Documents.Contains(document))
            {
                permit.Documents.Add(document);
                await context.SaveChangesAsync();
            }

            return permit;
        }

        public async Task<Permit> RemoveDocumentFromPermitAsync(int permitId, int documentId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .Include(p => p.Documents)
                .FirstOrDefaultAsync(p => p.Id == permitId);

            if (permit == null)
                throw new ArgumentException($"Permit with ID {permitId} not found");

            var document = permit.Documents.FirstOrDefault(d => d.Id == documentId);

            if (document != null)
            {
                permit.Documents.Remove(document);
                await context.SaveChangesAsync();
            }

            return permit;
        }

        public async Task ClearPermitTempFolderAsync(int permitId)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();

                var permit = await context.Permits
                    .FirstOrDefaultAsync(p => p.Id == permitId);

                if (permit == null)
                    throw new ArgumentException($"Permit with ID {permitId} not found");

                // Only clear temp folder for permits with no other state change allowed
                var finalStates = new[] { PermitStatus.CANCELLED, PermitStatus.EXPIRED, PermitStatus.VOIDED, PermitStatus.CLOSED };

                if (!finalStates.Contains(permit.Status))
                    throw new InvalidOperationException("Can only clear temp folder for permits in final states");

                // TODO: Implement actual temp folder deletion logic
                _logger.LogInformation("Cleared temp folder for permit {PermitId}", permitId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear temp folder for permit {PermitId}", permitId);
                throw;
            }
        }

        public async Task<Permit?> GetPermitByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Include(p => p.Job)
                .Include(p => p.Documents)
                .Include(p => p.OtherPermitsInUse)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Permit>> GetAllPermitsAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Include(p => p.Job)
                .Include(p => p.Documents)
                .ToListAsync();
        }

        private async Task<PermitReturn> ProcessPermitReturnSignatures(PermitReturnInput input, int permitId)
        {
            var processedCompetentPersons = new List<CompetentPerson>();
            var processedAuthorisedPersons = new List<AuthorisedPerson>();

            foreach (var cp in input.CompetentPersons)
            {
                var signatureFileId = await CopySignatureToTempAsync(int.Parse(cp.SignatureFileId), permitId);
                processedCompetentPersons.Add(new CompetentPerson
                {
                    WorkerId = cp.WorkerId,
                    Name = cp.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = cp.SignedAt
                });
            }

            foreach (var ap in input.AuthorisedPersons)
            {
                var signatureFileId = await CopySignatureToTempAsync(int.Parse(ap.SignatureFileId), permitId);
                processedAuthorisedPersons.Add(new AuthorisedPerson
                {
                    WorkerId = ap.WorkerId,
                    Name = ap.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = ap.SignedAt
                });
            }

            return new PermitReturn
            {
                CompetentPersons = processedCompetentPersons,
                AuthorisedPersons = processedAuthorisedPersons
            };
        }

        private void ValidateStateTransition(PermitStatus currentStatus, PermitStatus newStatus)
        {
            if (!_validStateTransitions.ContainsKey(currentStatus) ||
                !_validStateTransitions[currentStatus].Contains(newStatus))
            {
                throw new InvalidOperationException($"Invalid state transition from {currentStatus} to {newStatus}");
            }
        }
    }
}
