using System.ComponentModel.DataAnnotations;
using Shared.Enums;

namespace Shared.GraphQL.Models.Permits
{
    /// <summary>
    /// General Work Permit - derived from base Permit class
    /// </summary>
    public class GeneralWorkPermit : Permit
    {
        public GeneralWorkPermit()
        {
            PermitType = PermitType.GENERAL_WORK_PERMIT;
        }

        [StringLength(1000)]
        public string Isolation { get; set; } = string.Empty;

        // JSON array of work area inspection and permit renewal objects
        public List<WorkAreaInspection> WorkAreaInspectionAndPermitRenewal { get; set; } = new List<WorkAreaInspection>();
    }
}
