﻿using GraphQLApi.Services;
using GraphQLApi.Data;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Permits;
using Shared.Enums;
// using Task = Shared.GraphQL.Models.Task;
using HotChocolate.Data;
using Microsoft.EntityFrameworkCore;

namespace GraphQLApi.GraphQL.Queries
{
    public class Query
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly ITrainingStatusService _trainingStatusService;
        // private readonly ITaskService _taskService;
        private readonly IEquipmentService _equipmentService;
        private readonly IJobService _jobService;
        private readonly IToolboxService _toolboxService;
        private readonly IPermitService _permitService;

        public Query(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService,
            ITrainingStatusService trainingStatusService,
            // ITaskService taskService,
            IEquipmentService equipmentService,
            IJobService jobService,
            IToolboxService toolboxService,
            IPermitService permitService)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _trainingStatusService = trainingStatusService;
            // _taskService = taskService;
            _equipmentService = equipmentService;
            _jobService = jobService;
            _toolboxService = toolboxService;
            _permitService = permitService;
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Worker> GetAllWorkers([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Workers;
        }

        [UseProjection]
        public IQueryable<Worker> GetWorkerById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            var w = context.Workers.Where(w => w.Id == id);
            return w;
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Training> GetAllTrainings([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Trainings;
        }

        [UseProjection]
        public IQueryable<Training> GetTrainingById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Trainings.Where(t => t.Id == id);
        }

        public async Task<IEnumerable<Trade>> GetAllTrades()
        {
            return await _tradeService.GetAllTradesAsync();
        }

        public async Task<Trade?> GetTradeById(int id)
        {
            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<IEnumerable<Skill>> GetAllSkills()
        {
            return await _skillService.GetAllSkillsAsync();
        }

        public async Task<Skill?> GetSkillById(int id)
        {
            return await _skillService.GetSkillByIdAsync(id);
        }

        // Training History Queries
        public async Task<IEnumerable<WorkerTrainingHistory>> GetWorkerTrainingHistory(int workerId)
        {
            return await _trainingStatusService.GetWorkerTrainingHistoryAsync(workerId);
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiringTrainings(int daysAhead = 30)
        {
            return await _trainingStatusService.GetExpiringTrainingsAsync(daysAhead);
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiredTrainings()
        {
            return await _trainingStatusService.GetExpiredTrainingsAsync();
        }

        // // Task Queries
        // [UseProjection]
        // [UseFiltering]
        // public IQueryable<Task> GetAllTasks([Service] IDbContextFactory<AppDbContext> contextFactory)
        // {
        //     var context = contextFactory.CreateDbContext();
        //     return context.Tasks;
        // }

        // [UseProjection]
        // public IQueryable<Task> GetTaskById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        // {
        //     var context = contextFactory.CreateDbContext();
        //     return context.Tasks.Where(t => t.Id == id);
        // }

        // public async Task<IEnumerable<Task>> GetTasksByWorkerId(int workerId)
        // {
        //     return await _taskService.GetTasksByWorkerIdAsync(workerId);
        // }

        // public async Task<IEnumerable<Task>> GetTasksByChiefEngineerId(int chiefEngineerId)
        // {
        //     return await _taskService.GetTasksByChiefEngineerIdAsync(chiefEngineerId);
        // }

        // public async Task<IEnumerable<Task>> GetTasksByStatus(Shared.Enums.TaskStatus status)
        // {
        //     return await _taskService.GetTasksByStatusAsync(status);
        // }

        // public async Task<IEnumerable<Task>> GetTasksByPriority(Shared.Enums.TaskPriority priority)
        // {
        //     return await _taskService.GetTasksByPriorityAsync(priority);
        // }

        // Equipment Queries
        public async Task<IEnumerable<Equipment>> GetAllEquipment()
        {
            return await _equipmentService.GetAllEquipmentAsync();
        }

        public async Task<Equipment?> GetEquipmentById(int id)
        {
            return await _equipmentService.GetEquipmentByIdAsync(id);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByStatus(string status)
        {
            return await _equipmentService.GetEquipmentByStatusAsync(status);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByCategory(string category)
        {
            return await _equipmentService.GetEquipmentByCategoryAsync(category);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByLocation(string location)
        {
            return await _equipmentService.GetEquipmentByLocationAsync(location);
        }

        // Job Queries
        [UseProjection]
        [UseFiltering]
        public IQueryable<Job> GetAllJobs([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Jobs;
        }

        [UseProjection]
        public IQueryable<Job> GetJobById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Jobs.Where(j => j.Id == id);
        }

        public async Task<IEnumerable<Job>> GetRequestedJobs()
        {
            return await _jobService.GetRequestedJobsAsync();
        }

        public async Task<IEnumerable<Job>> GetBlockedJobs()
        {
            return await _jobService.GetBlockedJobsAsync();
        }

        public async Task<IEnumerable<Job>> GetPendingApprovalJobs()
        {
            return await _jobService.GetPendingApprovalJobsAsync();
        }

        public async Task<IEnumerable<Job>> GetApprovedJobs()
        {
            return await _jobService.GetApprovedJobsAsync();
        }

        public async Task<IEnumerable<Job>> GetDisapprovedJobs()
        {
            return await _jobService.GetDisapprovedJobsAsync();
        }

        public async Task<IEnumerable<Job>> GetFinishedJobs()
        {
            return await _jobService.GetFinishedJobsAsync();
        }

        public async Task<IEnumerable<Job>> GetJobsByStatus(Shared.Enums.JobStatus status)
        {
            return await _jobService.GetJobsByStatusAsync(status);
        }

        public async Task<IEnumerable<Job>> GetJobsByWorkerId(int workerId)
        {
            return await _jobService.GetJobsByWorkerIdAsync(workerId);
        }

        public async Task<IEnumerable<Job>> GetJobsByChiefEngineerId(int chiefEngineerId)
        {
            return await _jobService.GetJobsByChiefEngineerIdAsync(chiefEngineerId);
        }

        // Category Queries
        [UseProjection]
        [UseFiltering]
        public IQueryable<Category> GetAllCategories([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Categories;
        }

        [UseProjection]
        public IQueryable<Category> GetCategoryById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Categories.Where(c => c.Id == id);
        }

        // Toolbox Queries
        public async Task<IEnumerable<GraphQLApi.GraphQL.Types.TodaysJobRiskAssessment>> GetTodaysJobRiskAssessment()
        {
            var serviceResult = await _toolboxService.GetTodaysJobRiskAssessmentAsync();
            return serviceResult.Select(r => new GraphQLApi.GraphQL.Types.TodaysJobRiskAssessment
            {
                Id = r.Id,
                Title = r.Title,
                Hazards = new GraphQLApi.GraphQL.Types.TodaysJobHazard
                {
                    Id = r.Hazards.Id,
                    Description = r.Hazards.Description,
                    ControlMeasures = r.Hazards.ControlMeasures.Select(cm => new GraphQLApi.GraphQL.Types.TodaysJobControlMeasure
                    {
                        Id = cm.Id,
                        Description = cm.Description
                    }).ToList()
                }
            });
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Toolbox> GetAllToolboxes([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Toolboxes;
        }

        [UseProjection]
        public IQueryable<Toolbox> GetToolboxById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Toolboxes.Where(t => t.Id == id);
        }

        public async Task<Toolbox?> GetTodaysToolbox()
        {
            return await _toolboxService.GetTodaysToolboxAsync();
        }

        // Permit Queries
        public async Task<IEnumerable<GraphQLApi.GraphQL.Types.ToolboxRiskAssessment>> GetToolboxRiskAssessment(int jobId)
        {
            var serviceResult = await _permitService.GetToolboxRiskAssessmentAsync(jobId);
            return serviceResult.Select(r => new GraphQLApi.GraphQL.Types.ToolboxRiskAssessment
            {
                Id = r.Id,
                Title = r.Title,
                Hazards = new GraphQLApi.GraphQL.Types.ToolboxRiskAssessmentHazard
                {
                    Id = r.Hazards.Id,
                    Description = r.Hazards.Description,
                    ControlMeasures = r.Hazards.ControlMeasures.Select(cm => new GraphQLApi.GraphQL.Types.ToolboxRiskAssessmentControlMeasure
                    {
                        Id = cm.Id,
                        Description = cm.Description
                    }).ToList()
                }
            });
        }

        public async Task<bool> VerifySignoff(List<int> workerIds)
        {
            return await _permitService.VerifySignoffAsync(workerIds);
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Permit> GetAllPermits([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Permits;
        }

        [UseProjection]
        public IQueryable<Permit> GetPermitById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Permits.Where(p => p.Id == id);
        }

        public async Task<IEnumerable<Permit>> GetPermitsByJob(int jobId)
        {
            return await _permitService.GetPermitsByJobAsync(jobId);
        }

        public async Task<IEnumerable<Permit>> GetPermitsByStatus(PermitStatus status)
        {
            return await _permitService.GetPermitsByStatusAsync(status);
        }

        public async Task<IEnumerable<Permit>> GetPermitsByDate(DateTime date)
        {
            return await _permitService.GetPermitsByDateAsync(date);
        }
    }
}
