using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;
using Shared.Enums;

namespace GraphQLApi.Data.Configurations
{
    public class PermitConfiguration : IEntityTypeConfiguration<Permit>
    {
        public void Configure(EntityTypeBuilder<Permit> builder)
        {
            // Configure TPH inheritance
            builder.HasDiscriminator<PermitType>("PermitType")
                .HasValue<GeneralWorkPermit>(PermitType.GENERAL_WORK_PERMIT)
                .HasValue<HotWorkPermit>(PermitType.HOT_WORK_PERMIT)
                .HasValue<ExcavationWorkPermit>(PermitType.EXCAVATION_PERMIT)
                .HasValue<WorkAtHeightPermit>(PermitType.WORK_AT_HEIGHT_PERMIT)
                .HasValue<ConfinedSpacePermit>(PermitType.CONFINED_SPACE_ENTRY_PERMIT);

            // Primary key
            builder.HasKey(p => p.Id);

            // Configure properties
            builder.Property(p => p.PTWRefNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(p => p.ProjectName)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(p => p.StartingDateTime)
                .IsRequired();

            builder.Property(p => p.EndingDateTime)
                .IsRequired();

            builder.Property(p => p.Description)
                .HasMaxLength(1000);

            builder.Property(p => p.Location)
                .HasMaxLength(200);

            builder.Property(p => p.Hazards)
                .HasMaxLength(2000);

            builder.Property(p => p.PrecautionsRequired)
                .HasMaxLength(2000);

            builder.Property(p => p.PPE)
                .HasMaxLength(1000);

            builder.Property(p => p.Status)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(p => p.DaysValid)
                .IsRequired();

            // JSON fields are now configured as owned entities in AppDbContext

            // Configure relationships
            builder.HasOne(p => p.Job)
                .WithMany(j => j.Permits)
                .HasForeignKey(p => p.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            // Self-referencing many-to-many relationship for other permits
            builder.HasMany(p => p.OtherPermitsInUse)
                .WithMany(p => p.ReferencedByPermits)
                .UsingEntity("PermitOtherPermits",
                    l => l.HasOne(typeof(Permit)).WithMany().HasForeignKey("OtherPermitId"),
                    r => r.HasOne(typeof(Permit)).WithMany().HasForeignKey("PermitId"));

            // Many-to-many relationship with documents
            builder.HasMany(p => p.Documents)
                .WithMany()
                .UsingEntity("PermitDocuments");

            // Audit fields
            builder.Property(p => p.CreatedAt)
                .IsRequired();

            builder.Property(p => p.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(p => p.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(p => p.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(p => p.DeletedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(p => p.PTWRefNumber)
                .IsUnique();

            builder.HasIndex(p => p.Status);
            builder.HasIndex(p => p.StartingDateTime);
            builder.HasIndex(p => p.EndingDateTime);
            builder.HasIndex(p => p.JobId);

            builder.OwnsOne(
                p => p.PermitIssuer, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson();
                    OwnedNavigationBuilder.OwnsMany(p => p.CompetentPersons);
                    OwnedNavigationBuilder.OwnsMany(p => p.AuthorisedPersons);
                }
            );
            builder.OwnsOne(
                p => p.PermitReturn, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson();
                    OwnedNavigationBuilder.OwnsMany(p => p.CompetentPersons);
                    OwnedNavigationBuilder.OwnsMany(p => p.AuthorisedPersons);
                }
            );
            builder.OwnsOne(
                p => p.SignOff, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson();
                    OwnedNavigationBuilder.OwnsMany(p => p.Workers);
                }
            );
        }
    }
}
