using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using System.Text.Json;

namespace GraphQLApi.Data.Configurations
{
    public class ToolboxConfiguration : IEntityTypeConfiguration<Toolbox>
    {
        public void Configure(EntityTypeBuilder<Toolbox> builder)
        {
            builder.HasKey(t => t.Id);

            builder.Property(t => t.Date)
                .IsRequired();

            builder.Property(t => t.Status)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(t => t.EmergencyProcedures)
                .IsRequired()
                .HasMaxLength(2000);

            builder.Property(t => t.ToolboxTrainingTopics)
                .IsRequired()
                .HasMaxLength(2000);

            builder.Property(t => t.ClosedDate);

            // Configure JSON columns
            builder.Property(t => t.Conductor)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                    v => JsonSerializer.Deserialize<ToolboxConductor>(v, (JsonSerializerOptions)null!) ?? new ToolboxConductor())
                .HasColumnType("nvarchar(max)");

            builder.Property(t => t.ConductedBy)
                .HasConversion(
                    v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                    v => v == null ? null : JsonSerializer.Deserialize<ToolboxConductor>(v, (JsonSerializerOptions)null!))
                .HasColumnType("nvarchar(max)");

            builder.Property(t => t.Attendees)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                    v => JsonSerializer.Deserialize<ICollection<ToolboxAttendee>>(v, (JsonSerializerOptions)null!) ?? new List<ToolboxAttendee>())
                .HasColumnType("nvarchar(max)");

            // Many-to-many relationship with Jobs
            builder.HasMany(t => t.Jobs)
                .WithMany()
                .UsingEntity("ToolboxJobs");

            // Audit fields
            builder.Property(t => t.CreatedAt)
                .IsRequired();

            builder.Property(t => t.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(t => t.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(t => t.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(t => t.DeletedBy)
                .HasMaxLength(100);

            // Indexes for better query performance
            builder.HasIndex(t => t.Date);
            builder.HasIndex(t => t.Status);
            builder.HasIndex(t => t.IsDeleted);
        }
    }
}
