using System;
using System.Collections.Generic;
using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    public class Job : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public JobStatus Status { get; set; } = JobStatus.REQUESTED;
        public ICollection<PermitType> RequiredPermits { get; set; } = new List<PermitType>();
        public string? TimeForCompletion { get; set; }
        public DateTime StartDate { get; set; } = DateTime.Today.AddDays(1); // Default to tomorrow
        public DateTime? DueDate { get; set; }

        // Category relationship
        public int? CategoryId { get; set; }
        public Category? Category { get; set; }

        // Request information
        public int? RequestedById { get; set; }
        public Worker? RequestedBy { get; set; }
        public DateTime? RequestedDate { get; set; }

        // Block information
        public int? BlockedById { get; set; }
        public Worker? BlockedBy { get; set; }
        public DateTime? BlockedDate { get; set; }

        // Review information
        public int? ReviewedById { get; set; }
        public Worker? ReviewedBy { get; set; }
        public DateTime? ReviewedDate { get; set; }

        // Approval information (used for both approved and disapproved)
        public int? ApprovedById { get; set; }
        public Worker? ApprovedBy { get; set; }
        public DateTime? ApprovedDate { get; set; }

        // Finish information
        public int? FinishedById { get; set; }
        public Worker? FinishedBy { get; set; }
        public DateTime? FinishedDate { get; set; }

        // Chief Engineer and Workers
        public int? ChiefEngineerId { get; set; }
        public Worker? ChiefEngineer { get; set; }
        public ICollection<Worker> Workers { get; set; } = new List<Worker>();

        // Hazards, Documents, and Permits
        public ICollection<Hazard> Hazards { get; set; } = new List<Hazard>();
        public ICollection<DocumentFile> Documents { get; set; } = new List<DocumentFile>();
        public ICollection<Permits.Permit> Permits { get; set; } = new List<Permits.Permit>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }

        // Computed property for due date
        public DateTime CalculatedDueDate
        {
            get
            {
                if (DueDate.HasValue)
                    return DueDate.Value;

                // Calculate based on start date + time for completion
                if (!string.IsNullOrEmpty(TimeForCompletion) && TimeSpan.TryParse(TimeForCompletion, out var duration))
                {
                    return StartDate.Add(duration);
                }

                // Default to start date + 1 day if no time for completion specified
                return StartDate.AddDays(1);
            }
        }
    }
}
