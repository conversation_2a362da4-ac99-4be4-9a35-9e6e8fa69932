﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePermitJsonStorage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "WorkAtHeightPermit_InspectionAuthorization",
                table: "Permits",
                newName: "WorkAtHeightInspectionAuthorization");

            migrationBuilder.RenameColumn(
                name: "InspectionAuthorization",
                table: "Permits",
                newName: "ExcavationInspectionAuthorization");

            migrationBuilder.AlterColumn<string>(
                name: "PermitReturn",
                table: "Permits",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "WorkAtHeightInspectionAuthorization",
                table: "Permits",
                newName: "WorkAtHeightPermit_InspectionAuthorization");

            migrationBuilder.RenameColumn(
                name: "ExcavationInspectionAuthorization",
                table: "Permits",
                newName: "InspectionAuthorization");

            migrationBuilder.AlterColumn<string>(
                name: "PermitReturn",
                table: "Permits",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");
        }
    }
}
