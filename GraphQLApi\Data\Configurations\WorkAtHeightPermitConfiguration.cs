using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Permits;

namespace GraphQLApi.Data.Configurations
{
    public class WorkAtHeightPermitConfiguration : IEntityTypeConfiguration<WorkAtHeightPermit>
    {
        public void Configure(EntityTypeBuilder<WorkAtHeightPermit> builder)
        {
            builder.Property(p => p.ModeOfAccessToBeUsed)
                .HasMaxLength(500);

            builder.Property(p => p.Inspections)
                .HasMaxLength(1000);

            // InspectionAuthorization is now configured as owned entity in AppDbContext
                        builder.OwnsOne(
                p => p.InspectionAuthorization, OwnedNavigationBuilder =>
                {
                    OwnedNavigationBuilder.ToJson("WorkAtHeightInspectionAuthorization");
                }
            );

        }
    }
}
