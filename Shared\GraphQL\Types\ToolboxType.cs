using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class ToolboxType : ObjectType<Toolbox>
    {
        protected override void Configure(IObjectTypeDescriptor<Toolbox> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Date).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.Status).Type<NonNullType<EnumType<Shared.Enums.ToolboxStatus>>>();
            descriptor.Field(t => t.EmergencyProcedures).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.ToolboxTrainingTopics).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.ClosedDate).Type<DateTimeType>();

            // JSON objects
            descriptor.Field(t => t.Conductor).Type<ToolboxConductorType>();
            descriptor.Field(t => t.ConductedBy).Type<ToolboxConductorType>();
            descriptor.Field(t => t.Attendees).Type<ListType<ToolboxAttendeeType>>();

            // Relationships
            descriptor.Field(t => t.Jobs).Type<ListType<JobType>>();

            // Audit fields
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<StringType>();
        }
    }

    public class ToolboxConductorType : ObjectType<ToolboxConductor>
    {
        protected override void Configure(IObjectTypeDescriptor<ToolboxConductor> descriptor)
        {
            descriptor.Field(tc => tc.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(tc => tc.Name).Type<NonNullType<StringType>>();
            descriptor.Field(tc => tc.SignatureFileId).Type<NonNullType<StringType>>();
        }
    }

    public class ToolboxAttendeeType : ObjectType<ToolboxAttendee>
    {
        protected override void Configure(IObjectTypeDescriptor<ToolboxAttendee> descriptor)
        {
            descriptor.Field(ta => ta.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(ta => ta.Name).Type<NonNullType<StringType>>();
            descriptor.Field(ta => ta.Designation).Type<NonNullType<StringType>>();
            descriptor.Field(ta => ta.SignatureFileId).Type<NonNullType<StringType>>();
        }
    }
}
