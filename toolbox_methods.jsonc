{
    // comment 1: when dealing with signatureFileId a worker id is submited e.g in workers in sign off, competent person etc. the workers signaturefile is copied to the minIO temp bucket and folder for the permit with a signature folder with the file and id is copied to the permit signature file id. This process should as parrallel as possible and if one fails all fails and permit creation fails
    // comment 2: only one toolbox a day
    "valid state changes": {
        // comment 2: from the key to the value array are the only valid state changes to    
        "filled": [
            "pending closure"
        ],
        "pending closure": [
            "closed"
        ],
        "closed": []
    },
    "get todays job risk assessment": {
        "args": "none",
        "return": [ // returns an array of structure below
            {
                "Id": "int", // this is job id
                "Title": "string", // this is job title
                "hazards": {
                    "Id": "int",
                    "Description": "string",
                    "control measures": [
                        {
                            "Id": "int",
                            "Description": "string"
                        }
                    ]
                }
            }
        ],
        "actions": [
            "gets the todays approved job", // must be approved jobs
            "gets the hazards for the job",
            "gets the control measures for the hazards"
        ]
    },
    "create toolbox": {
        "args": {
            "conductorId": "int",
            "jobs": [ // array of this structure
                {
                    "jobId": "int",
                    "hazard": {
                        "Id": "int",
                        "description": "string",
                        "control measures": [
                            {
                                "Id": "int",
                                "description": "string"
                            }
                        ]
                    }
                }
            ],
            "emergency procedures": "string",
            "toolbox training topics": "string"
        },
        "return": "toolboxId",
        "actions": [
            "creates a toolbox with the parts passed in args",
            "updates control measures and hazards",
            "closed becames false",
            "status becames filled"
        ]
    },
    "add attendees": {
        "args": {
            "workers": [
                {
                    "workerId": "int",
                    "name": "string",
                    "designation": "string", // training for the worker
                    "signatureFileId": "string"
                }
            ]
        },
        "return": "none",
        "actions": [
            "adds workers to the toolbox",
            "status becames pending closure"
        ]
    },
    "summarize toolbox": {
        "args": [ // returns an array of structure below
            {
                "Id": "int", // this is job id
                "Title": "string", // this is job title
                "hazards": {
                    "Description": "string",
                    "control measures": [
                        {
                            "Description": "string"
                        }
                    ]
                }
            }
        ],
        "return": "none",
        "actions": [
            "adds new hazards to job, and control measures to the hazards",
            "marks the control measure as closed(set closed to true)"
        ]
    },
    "add hazard": {
        "args": {
            "jobId": "int",
            "description": "string",
            "control measures": [
                "strings"
            ]
        },
        "return": "none",
        "actions": [
            "adds hazard to the toolbox",
            "creates associated control measures",
            "links to the relevant job"
        ]
    },
    "add control measure": {
        "args": {
            "hazardId": "int",
            "description": "string"
        },
        "return": "none",
        "actions": [
            "creates the control measure",
            "adds the control measure to the hazard"
        ]
    },
    "clear permit temp folder": {
        "args": "permit id",
        "return": "none",
        "actions": [
            "delete permit temp folder",
            "only for mermits with no other state change allowed e.g closed"
        ]
    }
}