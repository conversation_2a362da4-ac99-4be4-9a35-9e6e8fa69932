﻿// <auto-generated />
using System;
using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace GraphQLApi.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("IncidentWorker", b =>
                {
                    b.Property<int>("IncidentsId")
                        .HasColumnType("int");

                    b.Property<int>("WorkersId")
                        .HasColumnType("int");

                    b.<PERSON>("IncidentsId", "WorkersId");

                    b.<PERSON>("WorkersId");

                    b.ToTable("IncidentWorkers", (string)null);
                });

            modelBuilder.Entity("JobDocuments", b =>
                {
                    b.Property<int>("DocumentsId")
                        .HasColumnType("int");

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.HasKey("DocumentsId", "JobId");

                    b.HasIndex("JobId");

                    b.ToTable("JobDocuments");
                });

            modelBuilder.Entity("JobWorkers", b =>
                {
                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<int>("WorkersId")
                        .HasColumnType("int");

                    b.HasKey("JobId", "WorkersId");

                    b.HasIndex("WorkersId");

                    b.ToTable("JobWorkers");
                });

            modelBuilder.Entity("PermitDocuments", b =>
                {
                    b.Property<int>("DocumentsId")
                        .HasColumnType("int");

                    b.Property<int>("PermitId")
                        .HasColumnType("int");

                    b.HasKey("DocumentsId", "PermitId");

                    b.HasIndex("PermitId");

                    b.ToTable("PermitDocuments");
                });

            modelBuilder.Entity("PermitOtherPermits", b =>
                {
                    b.Property<int>("OtherPermitId")
                        .HasColumnType("int");

                    b.Property<int>("PermitId")
                        .HasColumnType("int");

                    b.HasKey("OtherPermitId", "PermitId");

                    b.HasIndex("PermitId");

                    b.ToTable("PermitOtherPermits");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Description");

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.ControlMeasure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Closed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("HazardId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("HazardId");

                    b.ToTable("ControlMeasures");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.DocumentFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EntityType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("FileMetadataId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("TrainingId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("WorkerId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_DocumentFiles_CreatedAt");

                    b.HasIndex("FileMetadataId")
                        .HasDatabaseName("IX_DocumentFiles_FileMetadataId");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_DocumentFiles_IsDeleted");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_DocumentFiles_Name");

                    b.HasIndex("TrainingId");

                    b.HasIndex("WorkerId");

                    b.HasIndex("EntityType", "TrainingId")
                        .HasDatabaseName("IX_DocumentFiles_Training")
                        .HasFilter("[EntityType] = 'Training' AND [TrainingId] IS NOT NULL");

                    b.HasIndex("EntityType", "WorkerId")
                        .HasDatabaseName("IX_DocumentFiles_Worker")
                        .HasFilter("[EntityType] = 'Worker' AND [WorkerId] IS NOT NULL");

                    b.ToTable("DocumentFiles", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Equipment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastMaintenanceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Manufacturer")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Model")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("NextMaintenanceDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("PurchaseDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("PurchasePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("SerialNumber")
                        .IsUnique()
                        .HasFilter("[SerialNumber] IS NOT NULL");

                    b.ToTable("Equipment");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.FileMetadata", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalMetadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BucketName")
                        .IsRequired()
                        .HasMaxLength(63)
                        .HasColumnType("nvarchar(63)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ETag")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FolderPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsPublic")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ObjectKey")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<long>("Size")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Version")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("BucketName")
                        .HasDatabaseName("IX_FileMetadata_BucketName");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_FileMetadata_CreatedAt");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_FileMetadata_ExpiresAt")
                        .HasFilter("[ExpiresAt] IS NOT NULL");

                    b.HasIndex("FileType")
                        .HasDatabaseName("IX_FileMetadata_FileType");

                    b.HasIndex("FolderPath")
                        .HasDatabaseName("IX_FileMetadata_FolderPath")
                        .HasFilter("[FolderPath] IS NOT NULL");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_FileMetadata_IsDeleted");

                    b.HasIndex("ObjectKey")
                        .HasDatabaseName("IX_FileMetadata_ObjectKey");

                    b.HasIndex("BucketName", "ObjectKey")
                        .IsUnique()
                        .HasDatabaseName("IX_FileMetadata_BucketName_ObjectKey_Unique")
                        .HasFilter("[IsDeleted] = 0");

                    b.ToTable("FileMetadata", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Hazard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("Hazards");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Incident", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("IncidentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InvestigatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ReportedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Resolution")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("ResolvedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IncidentDate");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Status");

                    b.ToTable("Incidents", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Job", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ApprovedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("BlockedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("BlockedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("ChiefEngineerId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("FinishedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("FinishedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int?>("RequestedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RequestedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("RequiredPermits")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ReviewedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TimeForCompletion")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("BlockedById");

                    b.HasIndex("CategoryId");

                    b.HasIndex("ChiefEngineerId");

                    b.HasIndex("DueDate");

                    b.HasIndex("FinishedById");

                    b.HasIndex("RequestedById");

                    b.HasIndex("ReviewedById");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.ToTable("Jobs");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.Permit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("DaysValid")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("EndingDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Hazards")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("PPE")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("PTWRefNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("PermitType")
                        .HasColumnType("int");

                    b.Property<string>("PrecautionsRequired")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("StartingDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("EndingDateTime");

                    b.HasIndex("JobId");

                    b.HasIndex("PTWRefNumber")
                        .IsUnique();

                    b.HasIndex("StartingDateTime");

                    b.HasIndex("Status");

                    b.ToTable("Permits");

                    b.HasDiscriminator<int>("PermitType");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Skill", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Skills", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Toolbox", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Attendees")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ConductedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Conductor")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EmergencyProcedures")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ToolboxTrainingTopics")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Date");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Status");

                    b.ToTable("Toolboxes");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.ToolboxAttendance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("ToolboxSessionId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("WasPresent")
                        .HasColumnType("bit");

                    b.Property<int>("WorkerId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ToolboxSessionId");

                    b.HasIndex("WorkerId");

                    b.HasIndex("ToolboxSessionId", "WorkerId")
                        .IsUnique();

                    b.ToTable("ToolboxAttendances", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.ToolboxSession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Conductor")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("FileMetadataId")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("PhotoUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("SessionTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("Conductor");

                    b.HasIndex("FileMetadataId");

                    b.HasIndex("SessionTime");

                    b.ToTable("ToolboxSessions", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Trade", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Trades", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Training", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Duration")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Frequency")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("Scheduled");

                    b.Property<string>("Trainer")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TrainingType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ValidityPeriodMonths")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.ToTable("Trainings", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Worker", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Company")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateOnly?>("DateOfBirth")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("FileMetadataId")
                        .HasColumnType("int");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("InductionDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("ManHours")
                        .HasColumnType("int");

                    b.Property<DateTime?>("MedicalCheckDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MpesaNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NationalId")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("ProfilePictureFileId")
                        .HasColumnType("int");

                    b.Property<double>("Rating")
                        .HasColumnType("float");

                    b.Property<int?>("SignatureFileId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("Company");

                    b.HasIndex("Email");

                    b.HasIndex("FileMetadataId");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("NationalId")
                        .IsUnique();

                    b.HasIndex("ProfilePictureFileId");

                    b.HasIndex("SignatureFileId");

                    b.ToTable("Workers", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.WorkerAttendance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CheckInTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CheckOutTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsVerifiedByHikvision")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("WorkerId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CheckInTime");

                    b.HasIndex("WorkerId");

                    b.HasIndex("WorkerId", "CheckInTime");

                    b.ToTable("WorkerAttendances", (string)null);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.WorkerTrainingHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CertificateUrl")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("CompletionDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<decimal?>("Score")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("TrainingId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("WorkerId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompletionDate");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("Status");

                    b.HasIndex("TrainingId");

                    b.HasIndex("WorkerId");

                    b.HasIndex("WorkerId", "TrainingId", "CompletionDate");

                    b.ToTable("WorkerTrainingHistory", (string)null);
                });

            modelBuilder.Entity("ToolboxJobs", b =>
                {
                    b.Property<int>("JobsId")
                        .HasColumnType("int");

                    b.Property<int>("ToolboxId")
                        .HasColumnType("int");

                    b.HasKey("JobsId", "ToolboxId");

                    b.HasIndex("ToolboxId");

                    b.ToTable("ToolboxJobs");
                });

            modelBuilder.Entity("WorkerSkill", b =>
                {
                    b.Property<int>("WorkerId")
                        .HasColumnType("int");

                    b.Property<int>("SkillId")
                        .HasColumnType("int");

                    b.HasKey("WorkerId", "SkillId");

                    b.HasIndex("SkillId");

                    b.ToTable("WorkerSkill");
                });

            modelBuilder.Entity("WorkerTrade", b =>
                {
                    b.Property<int>("WorkerId")
                        .HasColumnType("int");

                    b.Property<int>("TradeId")
                        .HasColumnType("int");

                    b.HasKey("WorkerId", "TradeId");

                    b.HasIndex("TradeId");

                    b.ToTable("WorkerTrade");
                });

            modelBuilder.Entity("WorkerTraining", b =>
                {
                    b.Property<int>("WorkerId")
                        .HasColumnType("int");

                    b.Property<int>("TrainingId")
                        .HasColumnType("int");

                    b.HasKey("WorkerId", "TrainingId");

                    b.HasIndex("TrainingId");

                    b.ToTable("WorkerTraining");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.ConfinedSpacePermit", b =>
                {
                    b.HasBaseType("Shared.GraphQL.Models.Permits.Permit");

                    b.Property<string>("EmergencyGuidelines")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("NameOfTrainingOrganization")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("WorkersHaveBeenTrained")
                        .HasColumnType("bit");

                    b.HasDiscriminator().HasValue(2);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.ExcavationWorkPermit", b =>
                {
                    b.HasBaseType("Shared.GraphQL.Models.Permits.Permit");

                    b.Property<string>("DepthOfExcavation")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Inspections")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ListOfEquipmentToBeUsed")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ProtectionSystems")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasDiscriminator().HasValue(4);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.GeneralWorkPermit", b =>
                {
                    b.HasBaseType("Shared.GraphQL.Models.Permits.Permit");

                    b.Property<string>("Isolation")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasDiscriminator().HasValue(0);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.HotWorkPermit", b =>
                {
                    b.HasBaseType("Shared.GraphQL.Models.Permits.Permit");

                    b.Property<string>("FireExtinguishers")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("NatureOfWork")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.WorkAtHeightPermit", b =>
                {
                    b.HasBaseType("Shared.GraphQL.Models.Permits.Permit");

                    b.Property<string>("Inspections")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ModeOfAccessToBeUsed")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.ToTable("Permits", t =>
                        {
                            t.Property("Inspections")
                                .HasColumnName("WorkAtHeightPermit_Inspections");
                        });

                    b.HasDiscriminator().HasValue(3);
                });

            modelBuilder.Entity("IncidentWorker", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Incident", null)
                        .WithMany()
                        .HasForeignKey("IncidentsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Worker", null)
                        .WithMany()
                        .HasForeignKey("WorkersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("JobDocuments", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.DocumentFile", null)
                        .WithMany()
                        .HasForeignKey("DocumentsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Job", null)
                        .WithMany()
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("JobWorkers", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Job", null)
                        .WithMany()
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Worker", null)
                        .WithMany()
                        .HasForeignKey("WorkersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PermitDocuments", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.DocumentFile", null)
                        .WithMany()
                        .HasForeignKey("DocumentsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Permits.Permit", null)
                        .WithMany()
                        .HasForeignKey("PermitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PermitOtherPermits", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Permits.Permit", null)
                        .WithMany()
                        .HasForeignKey("OtherPermitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Permits.Permit", null)
                        .WithMany()
                        .HasForeignKey("PermitId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.ControlMeasure", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Hazard", "Hazard")
                        .WithMany("ControlMeasures")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.DocumentFile", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.FileMetadata", "FileMetadata")
                        .WithMany()
                        .HasForeignKey("FileMetadataId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Training", null)
                        .WithMany("DocumentFiles")
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Shared.GraphQL.Models.Worker", null)
                        .WithMany("DocumentFiles")
                        .HasForeignKey("WorkerId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("FileMetadata");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Hazard", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Job", "Job")
                        .WithMany("Hazards")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Job", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Worker", "ApprovedBy")
                        .WithMany()
                        .HasForeignKey("ApprovedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Shared.GraphQL.Models.Worker", "BlockedBy")
                        .WithMany()
                        .HasForeignKey("BlockedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Shared.GraphQL.Models.Category", "Category")
                        .WithMany("Jobs")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Shared.GraphQL.Models.Worker", "ChiefEngineer")
                        .WithMany()
                        .HasForeignKey("ChiefEngineerId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Shared.GraphQL.Models.Worker", "FinishedBy")
                        .WithMany()
                        .HasForeignKey("FinishedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Shared.GraphQL.Models.Worker", "RequestedBy")
                        .WithMany()
                        .HasForeignKey("RequestedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Shared.GraphQL.Models.Worker", "ReviewedBy")
                        .WithMany()
                        .HasForeignKey("ReviewedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("ApprovedBy");

                    b.Navigation("BlockedBy");

                    b.Navigation("Category");

                    b.Navigation("ChiefEngineer");

                    b.Navigation("FinishedBy");

                    b.Navigation("RequestedBy");

                    b.Navigation("ReviewedBy");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.Permit", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Job", "Job")
                        .WithMany("Permits")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("Shared.GraphQL.Models.Permits.PermitIssuer", "PermitIssuer", b1 =>
                        {
                            b1.Property<int>("PermitId")
                                .HasColumnType("int");

                            b1.HasKey("PermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("PermitIssuer");

                            b1.WithOwner()
                                .HasForeignKey("PermitId");

                            b1.OwnsMany("Shared.GraphQL.Models.Permits.AuthorisedPerson", "AuthorisedPersons", b2 =>
                                {
                                    b2.Property<int>("PermitIssuerPermitId")
                                        .HasColumnType("int");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Name")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("SignatureFileId")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<DateTime>("SignedAt")
                                        .HasColumnType("datetime2");

                                    b2.Property<int>("WorkerId")
                                        .HasColumnType("int");

                                    b2.HasKey("PermitIssuerPermitId", "Id");

                                    b2.ToTable("Permits");

                                    b2.WithOwner()
                                        .HasForeignKey("PermitIssuerPermitId");
                                });

                            b1.OwnsMany("Shared.GraphQL.Models.Permits.CompetentPerson", "CompetentPersons", b2 =>
                                {
                                    b2.Property<int>("PermitIssuerPermitId")
                                        .HasColumnType("int");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Name")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("SignatureFileId")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<DateTime>("SignedAt")
                                        .HasColumnType("datetime2");

                                    b2.Property<int>("WorkerId")
                                        .HasColumnType("int");

                                    b2.HasKey("PermitIssuerPermitId", "Id");

                                    b2.ToTable("Permits");

                                    b2.WithOwner()
                                        .HasForeignKey("PermitIssuerPermitId");
                                });

                            b1.Navigation("AuthorisedPersons");

                            b1.Navigation("CompetentPersons");
                        });

                    b.OwnsOne("Shared.GraphQL.Models.Permits.PermitReturn", "PermitReturn", b1 =>
                        {
                            b1.Property<int>("PermitId")
                                .HasColumnType("int");

                            b1.HasKey("PermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("PermitReturn");

                            b1.WithOwner()
                                .HasForeignKey("PermitId");

                            b1.OwnsMany("Shared.GraphQL.Models.Permits.AuthorisedPerson", "AuthorisedPersons", b2 =>
                                {
                                    b2.Property<int>("PermitReturnPermitId")
                                        .HasColumnType("int");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Name")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("SignatureFileId")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<DateTime>("SignedAt")
                                        .HasColumnType("datetime2");

                                    b2.Property<int>("WorkerId")
                                        .HasColumnType("int");

                                    b2.HasKey("PermitReturnPermitId", "Id");

                                    b2.ToTable("Permits");

                                    b2.WithOwner()
                                        .HasForeignKey("PermitReturnPermitId");
                                });

                            b1.OwnsMany("Shared.GraphQL.Models.Permits.CompetentPerson", "CompetentPersons", b2 =>
                                {
                                    b2.Property<int>("PermitReturnPermitId")
                                        .HasColumnType("int");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Name")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("SignatureFileId")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<DateTime>("SignedAt")
                                        .HasColumnType("datetime2");

                                    b2.Property<int>("WorkerId")
                                        .HasColumnType("int");

                                    b2.HasKey("PermitReturnPermitId", "Id");

                                    b2.ToTable("Permits");

                                    b2.WithOwner()
                                        .HasForeignKey("PermitReturnPermitId");
                                });

                            b1.Navigation("AuthorisedPersons");

                            b1.Navigation("CompetentPersons");
                        });

                    b.OwnsOne("Shared.GraphQL.Models.Permits.SignOff", "SignOff", b1 =>
                        {
                            b1.Property<int>("PermitId")
                                .HasColumnType("int");

                            b1.Property<DateTime>("DateTime")
                                .HasColumnType("datetime2");

                            b1.HasKey("PermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("SignOff");

                            b1.WithOwner()
                                .HasForeignKey("PermitId");

                            b1.OwnsMany("Shared.GraphQL.Models.Permits.PermitWorker", "Workers", b2 =>
                                {
                                    b2.Property<int>("SignOffPermitId")
                                        .HasColumnType("int");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("int");

                                    b2.Property<string>("Designation")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("Name")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<string>("SignatureFileId")
                                        .IsRequired()
                                        .HasColumnType("nvarchar(max)");

                                    b2.Property<DateTime>("SignedAt")
                                        .HasColumnType("datetime2");

                                    b2.Property<int>("WorkerId")
                                        .HasColumnType("int");

                                    b2.HasKey("SignOffPermitId", "Id");

                                    b2.ToTable("Permits");

                                    b2.WithOwner()
                                        .HasForeignKey("SignOffPermitId");
                                });

                            b1.Navigation("Workers");
                        });

                    b.Navigation("Job");

                    b.Navigation("PermitIssuer")
                        .IsRequired();

                    b.Navigation("PermitReturn");

                    b.Navigation("SignOff")
                        .IsRequired();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.ToolboxAttendance", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.ToolboxSession", "ToolboxSession")
                        .WithMany("Attendances")
                        .HasForeignKey("ToolboxSessionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Worker", "Worker")
                        .WithMany()
                        .HasForeignKey("WorkerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ToolboxSession");

                    b.Navigation("Worker");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.ToolboxSession", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.FileMetadata", null)
                        .WithMany("ToolboxSessions")
                        .HasForeignKey("FileMetadataId");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Worker", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.FileMetadata", null)
                        .WithMany("WorkersWithProfilePicture")
                        .HasForeignKey("FileMetadataId");

                    b.HasOne("Shared.GraphQL.Models.FileMetadata", "ProfilePictureFile")
                        .WithMany()
                        .HasForeignKey("ProfilePictureFileId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Shared.GraphQL.Models.FileMetadata", "SignatureFile")
                        .WithMany()
                        .HasForeignKey("SignatureFileId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("ProfilePictureFile");

                    b.Navigation("SignatureFile");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.WorkerAttendance", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Worker", "Worker")
                        .WithMany()
                        .HasForeignKey("WorkerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Worker");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.WorkerTrainingHistory", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Training", "Training")
                        .WithMany("TrainingHistory")
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Worker", "Worker")
                        .WithMany("TrainingHistory")
                        .HasForeignKey("WorkerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Training");

                    b.Navigation("Worker");
                });

            modelBuilder.Entity("ToolboxJobs", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Job", null)
                        .WithMany()
                        .HasForeignKey("JobsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Toolbox", null)
                        .WithMany()
                        .HasForeignKey("ToolboxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WorkerSkill", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Skill", null)
                        .WithMany()
                        .HasForeignKey("SkillId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Worker", null)
                        .WithMany()
                        .HasForeignKey("WorkerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WorkerTrade", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Trade", null)
                        .WithMany()
                        .HasForeignKey("TradeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Worker", null)
                        .WithMany()
                        .HasForeignKey("WorkerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WorkerTraining", b =>
                {
                    b.HasOne("Shared.GraphQL.Models.Training", null)
                        .WithMany()
                        .HasForeignKey("TrainingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Shared.GraphQL.Models.Worker", null)
                        .WithMany()
                        .HasForeignKey("WorkerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.ConfinedSpacePermit", b =>
                {
                    b.OwnsOne("Shared.GraphQL.Models.Permits.AtmosphericReading", "BottomReading", b1 =>
                        {
                            b1.Property<int>("ConfinedSpacePermitId")
                                .HasColumnType("int");

                            b1.Property<string>("Co2")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Explosive")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Oxygen")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Toxic")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ConfinedSpacePermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("BottomReading");

                            b1.WithOwner()
                                .HasForeignKey("ConfinedSpacePermitId");
                        });

                    b.OwnsOne("Shared.GraphQL.Models.Permits.AtmosphericReading", "MidReading", b1 =>
                        {
                            b1.Property<int>("ConfinedSpacePermitId")
                                .HasColumnType("int");

                            b1.Property<string>("Co2")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Explosive")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Oxygen")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Toxic")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ConfinedSpacePermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("MidReading");

                            b1.WithOwner()
                                .HasForeignKey("ConfinedSpacePermitId");
                        });

                    b.OwnsOne("Shared.GraphQL.Models.Permits.AtmosphericReading", "TopReading", b1 =>
                        {
                            b1.Property<int>("ConfinedSpacePermitId")
                                .HasColumnType("int");

                            b1.Property<string>("Co2")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Explosive")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Oxygen")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Toxic")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ConfinedSpacePermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("TopReading");

                            b1.WithOwner()
                                .HasForeignKey("ConfinedSpacePermitId");
                        });

                    b.OwnsOne("Shared.GraphQL.Models.Permits.TaskObserver", "TaskObserver", b1 =>
                        {
                            b1.Property<int>("ConfinedSpacePermitId")
                                .HasColumnType("int");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("SignatureFileId")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<DateTime>("SignedAt")
                                .HasColumnType("datetime2");

                            b1.Property<int>("WorkerId")
                                .HasColumnType("int");

                            b1.HasKey("ConfinedSpacePermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("TaskObserver");

                            b1.WithOwner()
                                .HasForeignKey("ConfinedSpacePermitId");
                        });

                    b.Navigation("BottomReading")
                        .IsRequired();

                    b.Navigation("MidReading")
                        .IsRequired();

                    b.Navigation("TaskObserver")
                        .IsRequired();

                    b.Navigation("TopReading")
                        .IsRequired();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.ExcavationWorkPermit", b =>
                {
                    b.OwnsOne("Shared.GraphQL.Models.Permits.InspectionAuthorization", "InspectionAuthorization", b1 =>
                        {
                            b1.Property<int>("ExcavationWorkPermitId")
                                .HasColumnType("int");

                            b1.Property<string>("Comments")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<DateTime>("DateOfInspection")
                                .HasColumnType("datetime2");

                            b1.Property<string>("Designation")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("NameOfInspector")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("ExcavationWorkPermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("ExcavationInspectionAuthorization");

                            b1.WithOwner()
                                .HasForeignKey("ExcavationWorkPermitId");
                        });

                    b.Navigation("InspectionAuthorization")
                        .IsRequired();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.GeneralWorkPermit", b =>
                {
                    b.OwnsMany("Shared.GraphQL.Models.Permits.WorkAreaInspection", "WorkAreaInspectionAndPermitRenewal", b1 =>
                        {
                            b1.Property<int>("GeneralWorkPermitId")
                                .HasColumnType("int");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<string>("Comments")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("SignatureFileId")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<DateTime>("SignedAt")
                                .HasColumnType("datetime2");

                            b1.HasKey("GeneralWorkPermitId", "Id");

                            b1.ToTable("Permits");

                            b1.ToJson("WorkAreaInspectionAndPermitRenewal");

                            b1.WithOwner()
                                .HasForeignKey("GeneralWorkPermitId");
                        });

                    b.Navigation("WorkAreaInspectionAndPermitRenewal");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.HotWorkPermit", b =>
                {
                    b.OwnsOne("Shared.GraphQL.Models.Permits.FireSafetySupervisor", "FireSafetySupervisor", b1 =>
                        {
                            b1.Property<int>("HotWorkPermitId")
                                .HasColumnType("int");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("SignatureFileId")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<DateTime>("SignedAt")
                                .HasColumnType("datetime2");

                            b1.Property<int>("WorkerId")
                                .HasColumnType("int");

                            b1.HasKey("HotWorkPermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("FireSafetySupervisor");

                            b1.WithOwner()
                                .HasForeignKey("HotWorkPermitId");
                        });

                    b.Navigation("FireSafetySupervisor")
                        .IsRequired();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Permits.WorkAtHeightPermit", b =>
                {
                    b.OwnsOne("Shared.GraphQL.Models.Permits.InspectionAuthorization", "InspectionAuthorization", b1 =>
                        {
                            b1.Property<int>("WorkAtHeightPermitId")
                                .HasColumnType("int");

                            b1.Property<string>("Comments")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<DateTime>("DateOfInspection")
                                .HasColumnType("datetime2");

                            b1.Property<string>("Designation")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("NameOfInspector")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("WorkAtHeightPermitId");

                            b1.ToTable("Permits");

                            b1.ToJson("WorkAtHeightInspectionAuthorization");

                            b1.WithOwner()
                                .HasForeignKey("WorkAtHeightPermitId");
                        });

                    b.Navigation("InspectionAuthorization")
                        .IsRequired();
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Category", b =>
                {
                    b.Navigation("Jobs");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.FileMetadata", b =>
                {
                    b.Navigation("ToolboxSessions");

                    b.Navigation("WorkersWithProfilePicture");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Hazard", b =>
                {
                    b.Navigation("ControlMeasures");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Job", b =>
                {
                    b.Navigation("Hazards");

                    b.Navigation("Permits");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.ToolboxSession", b =>
                {
                    b.Navigation("Attendances");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Training", b =>
                {
                    b.Navigation("DocumentFiles");

                    b.Navigation("TrainingHistory");
                });

            modelBuilder.Entity("Shared.GraphQL.Models.Worker", b =>
                {
                    b.Navigation("DocumentFiles");

                    b.Navigation("TrainingHistory");
                });
#pragma warning restore 612, 618
        }
    }
}
