using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Permits
{
    /// <summary>
    /// Base permit class for Table-per-Hierarchy inheritance
    /// </summary>
    public abstract class Permit : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }

        // Job relationship (compulsory)
        public int JobId { get; set; }
        public Job Job { get; set; } = null!;

        [Required]
        [StringLength(50)]
        public string PTWRefNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string ProjectName { get; set; } = string.Empty;

        public DateTime StartingDateTime { get; set; }
        public DateTime EndingDateTime { get; set; }

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(200)]
        public string Location { get; set; } = string.Empty;

        [StringLength(2000)]
        public string Hazards { get; set; } = string.Empty;

        [StringLength(2000)]
        public string PrecautionsRequired { get; set; } = string.Empty;

        [StringLength(1000)]
        public string PPE { get; set; } = string.Empty;

        public PermitStatus Status { get; set; } = PermitStatus.DRAFTED;

        public int DaysValid { get; set; } = 1;

        // JSON fields for complex structures
        public PermitIssuer PermitIssuer { get; set; } = null!; // JSON object
        public PermitReturn? PermitReturn { get; set; } // JSON object, nullable
        public SignOff SignOff { get; set; } = null!; // JSON object

        // Many-to-many relationship with other permits
        public ICollection<Permit> OtherPermitsInUse { get; set; } = new List<Permit>();
        public ICollection<Permit> ReferencedByPermits { get; set; } = new List<Permit>();

        // Many-to-many relationship with documents
        public ICollection<DocumentFile> Documents { get; set; } = new List<DocumentFile>();

        // Discriminator for TPH inheritance
        public virtual PermitType PermitType { get; protected set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
    public class PermitIssuer
    {
        public List<CompetentPerson> CompetentPersons { get; set; } = new List<CompetentPerson>();
        public List<AuthorisedPerson> AuthorisedPersons { get; set; } = new List<AuthorisedPerson>();
    }
    public class PermitReturn
    {
        public List<CompetentPerson> CompetentPersons { get; set; } = new List<CompetentPerson>();
        public List<AuthorisedPerson> AuthorisedPersons { get; set; } = new List<AuthorisedPerson>();
    }
    public class SignOff
    {
        public DateTime DateTime { get; set; }
        public List<PermitWorker> Workers { get; set; } = new List<PermitWorker>();
    }
    public class CompetentPerson
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }
    public class AuthorisedPerson
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }
    public class PermitWorker
    {
        public int WorkerId { get; set; }
        public string Designation { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    // JSON classes for derived permit types
    public class WorkAreaInspection
    {
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
        public string Comments { get; set; } = string.Empty;
    }

    public class InspectionAuthorization
    {
        public string NameOfInspector { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public DateTime DateOfInspection { get; set; }
        public string Comments { get; set; } = string.Empty;
    }

    public class AtmosphericReading
    {
        public string Oxygen { get; set; } = string.Empty;
        public string Explosive { get; set; } = string.Empty;
        public string Toxic { get; set; } = string.Empty;
        public string Co2 { get; set; } = string.Empty;
    }

    public class TaskObserver
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class FireSafetySupervisor
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }
}
