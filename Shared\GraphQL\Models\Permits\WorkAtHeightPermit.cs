using System.ComponentModel.DataAnnotations;
using Shared.Enums;

namespace Shared.GraphQL.Models.Permits
{
    /// <summary>
    /// Work at Height Permit - derived from base Permit class
    /// </summary>
    public class WorkAtHeightPermit : Permit
    {
        public WorkAtHeightPermit()
        {
            PermitType = PermitType.WORK_AT_HEIGHT_PERMIT;
        }

        [StringLength(500)]
        public string ModeOfAccessToBeUsed { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Inspections { get; set; } = string.Empty;

        // JSON object for inspection authorization
        public InspectionAuthorization InspectionAuthorization { get; set; } = null!;
    }
}
