using System.ComponentModel.DataAnnotations;
using Shared.Enums;

namespace Shared.GraphQL.Models.Permits
{
    /// <summary>
    /// Confined Space Permit - derived from base Permit class
    /// </summary>
    public class ConfinedSpacePermit : Permit
    {
        public ConfinedSpacePermit()
        {
            PermitType = PermitType.CONFINED_SPACE_ENTRY_PERMIT;
        }

        public bool WorkersHaveBeenTrained { get; set; }

        [StringLength(200)]
        public string NameOfTrainingOrganization { get; set; } = string.Empty;

        // JSON objects for readings
        public AtmosphericReading TopReading { get; set; } = null!;
        public AtmosphericReading MidReading { get; set; } = null!;
        public AtmosphericReading BottomReading { get; set; } = null!;

        [StringLength(2000)]
        public string EmergencyGuidelines { get; set; } = string.Empty;

        // JSON object for task observer
        public TaskObserver TaskObserver { get; set; } = null!;
    }
}
